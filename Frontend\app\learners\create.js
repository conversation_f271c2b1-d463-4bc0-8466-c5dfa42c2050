import React, { useState } from 'react';
import { View, StyleSheet, ScrollView, Alert } from 'react-native';
import {
  Text,
  Card,
  Title,
  Button,
  TextInput,
  HelperText,
  RadioButton,
  ActivityIndicator
} from 'react-native-paper';
import { router } from 'expo-router';
import { useMutation, useQuery } from '@apollo/client';
import { CREATE_LEARNER, REGISTER_USER } from '../../services/graphql/mutations';
import { GET_LEARNERS } from '../../services/graphql/queries';
import LearnerNavbar from '../../components/navigation/LearnerNavbar';

const LicenseTypes = [
  { value: 'PERMIS_A1', label: 'Permis A1 (Moto légère)' },
  { value: 'PERMIS_A', label: 'Permis A (Moto)' },
  { value: 'PERMIS_B', label: 'Permis B (Voiture)' },
  { value: 'PERMIS_BE', label: 'Permis BE (Voiture + remorque)' },
  { value: 'PERMIS_C', label: 'Permis C (Poids lourd)' },
  { value: 'PERMIS_CE', label: 'Permis CE (Poids lourd + remorque)' },
  { value: 'PERMIS_D', label: 'Permis D (Transport en commun)' },
  { value: 'PERMIS_D1', label: 'Permis D1 (Minibus)' },
  { value: 'PERMIS_DE', label: 'Permis DE (Bus + remorque)' },
  { value: 'PERMIS_H', label: 'Permis H (Véhicule handicapé)' }
];

const LearnerStatuses = [
  { value: 'INSCRIT', label: 'Inscrit' },
  { value: 'EN_COURS', label: 'En cours' },
  { value: 'TERMINE', label: 'Terminé' },
  { value: 'SUSPENDU', label: 'Suspendu' }
];

export default function CreateLearnerScreen() {
  const [formData, setFormData] = useState({
    // User information
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    address: '',

    // Learner specific information
    dateOfBirth: '',
    licenseType: 'PERMIS_B',
    cin: '',
    numberOfHours: '20',
    startDate: new Date().toISOString().split('T')[0],
    status: 'INSCRIT',

    // Additional information
    emergencyContact: '',
    emergencyPhone: '',
    medicalConditions: '',
    notes: ''
  });

  const [errors, setErrors] = useState({});

  const [registerUser] = useMutation(REGISTER_USER);
  const [createLearner, { loading: creating }] = useMutation(CREATE_LEARNER, {
    refetchQueries: [{ query: GET_LEARNERS }],
    onCompleted: (data) => {
      Alert.alert(
        'Succès',
        'L\'apprenant a été créé avec succès',
        [
          {
            text: 'OK',
            onPress: () => router.push('/learners')
          }
        ]
      );
    },
    onError: (error) => {
      Alert.alert(
        'Erreur',
        error.message || 'Une erreur est survenue lors de la création'
      );
    }
  });

  const validateForm = () => {
    const newErrors = {};

    // User information validation
    if (!formData.firstName.trim()) {
      newErrors.firstName = 'Le prénom est requis';
    }

    if (!formData.lastName.trim()) {
      newErrors.lastName = 'Le nom est requis';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'L\'email est requis';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Format d\'email invalide';
    }

    if (!formData.phone.trim()) {
      newErrors.phone = 'Le numéro de téléphone est requis';
    } else if (!/^\d{8}$/.test(formData.phone.replace(/\s/g, ''))) {
      newErrors.phone = 'Le numéro doit contenir 8 chiffres';
    }

    // Learner specific validation
    if (!formData.dateOfBirth) {
      newErrors.dateOfBirth = 'La date de naissance est requise';
    } else {
      const birthDate = new Date(formData.dateOfBirth);
      const today = new Date();
      const age = today.getFullYear() - birthDate.getFullYear();
      if (age < 16) {
        newErrors.dateOfBirth = 'L\'apprenant doit avoir au moins 16 ans';
      }
      if (age > 80) {
        newErrors.dateOfBirth = 'Âge maximum dépassé';
      }
    }

    if (!formData.cin.trim()) {
      newErrors.cin = 'Le numéro CIN est requis';
    } else if (!/^\d{8}$/.test(formData.cin)) {
      newErrors.cin = 'Le numéro CIN doit contenir exactement 8 chiffres';
    }

    if (!formData.numberOfHours || parseInt(formData.numberOfHours) < 1) {
      newErrors.numberOfHours = 'Le nombre d\'heures doit être supérieur à 0';
    } else if (parseInt(formData.numberOfHours) > 100) {
      newErrors.numberOfHours = 'Le nombre d\'heures ne peut pas dépasser 100';
    }

    if (!formData.startDate) {
      newErrors.startDate = 'La date de début est requise';
    } else {
      const startDate = new Date(formData.startDate);
      const today = new Date();
      if (startDate > new Date(today.getTime() + 30 * 24 * 60 * 60 * 1000)) {
        newErrors.startDate = 'La date de début ne peut pas être dans plus de 30 jours';
      }
    }

    // Emergency contact validation
    if (formData.emergencyContact.trim() && !formData.emergencyPhone.trim()) {
      newErrors.emergencyPhone = 'Le numéro d\'urgence est requis si un contact d\'urgence est spécifié';
    }

    if (formData.emergencyPhone.trim() && !/^\d{8}$/.test(formData.emergencyPhone.replace(/\s/g, ''))) {
      newErrors.emergencyPhone = 'Le numéro d\'urgence doit contenir 8 chiffres';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    try {
      // First create the user
      const userResult = await registerUser({
        variables: {
          input: {
            email: formData.email,
            password: 'temp123456', // Temporary password - should be changed by user
            firstName: formData.firstName,
            lastName: formData.lastName,
            phone: formData.phone,
            address: formData.address,
            role: 'APPRENANT'
          }
        }
      });

      if (userResult.data?.register?.user) {
        // Then create the learner with the user ID
        await createLearner({
          variables: {
            input: {
              userId: userResult.data.register.user.id,
              dateOfBirth: formData.dateOfBirth,
              licenseType: formData.licenseType,
              cin: formData.cin,
              numberOfHours: parseInt(formData.numberOfHours),
              startDate: formData.startDate,
              status: formData.status,
              emergencyContact: formData.emergencyContact,
              emergencyPhone: formData.emergencyPhone,
              medicalConditions: formData.medicalConditions,
              notes: formData.notes
            }
          }
        });
      }
    } catch (error) {
      console.error('Error creating learner:', error);
    }
  };

  const updateFormData = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: null }));
    }
  };

  return (
    <View style={styles.container}>
      {/* Custom Navbar */}
      <LearnerNavbar
        title="Créer un apprenant"
        showBackButton={true}
      />

      <ScrollView style={styles.scrollContainer}>
        <Card style={styles.card}>
        <Card.Content>
          <Title style={styles.title}>Créer un nouvel apprenant</Title>

          {/* Personal Information */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Informations personnelles</Text>

            <TextInput
              label="Prénom *"
              value={formData.firstName}
              onChangeText={(value) => updateFormData('firstName', value)}
              error={!!errors.firstName}
              style={styles.input}
            />
            <HelperText type="error" visible={!!errors.firstName}>
              {errors.firstName}
            </HelperText>

            <TextInput
              label="Nom *"
              value={formData.lastName}
              onChangeText={(value) => updateFormData('lastName', value)}
              error={!!errors.lastName}
              style={styles.input}
            />
            <HelperText type="error" visible={!!errors.lastName}>
              {errors.lastName}
            </HelperText>

            <TextInput
              label="Email *"
              value={formData.email}
              onChangeText={(value) => updateFormData('email', value)}
              keyboardType="email-address"
              autoCapitalize="none"
              error={!!errors.email}
              style={styles.input}
            />
            <HelperText type="error" visible={!!errors.email}>
              {errors.email}
            </HelperText>

            <TextInput
              label="Téléphone *"
              value={formData.phone}
              onChangeText={(value) => updateFormData('phone', value)}
              keyboardType="phone-pad"
              error={!!errors.phone}
              style={styles.input}
            />
            <HelperText type="error" visible={!!errors.phone}>
              {errors.phone}
            </HelperText>

            <TextInput
              label="Adresse"
              value={formData.address}
              onChangeText={(value) => updateFormData('address', value)}
              multiline
              numberOfLines={2}
              style={styles.input}
            />
          </View>

          {/* Learner Specific Information */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Informations de l'apprenant</Text>

            <TextInput
              label="Numéro CIN *"
              value={formData.cin}
              onChangeText={(value) => updateFormData('cin', value)}
              keyboardType="numeric"
              maxLength={8}
              error={!!errors.cin}
              style={styles.input}
            />
            <HelperText type="error" visible={!!errors.cin}>
              {errors.cin}
            </HelperText>

            <TextInput
              label="Date de naissance *"
              value={formData.dateOfBirth}
              onChangeText={(value) => updateFormData('dateOfBirth', value)}
              placeholder="YYYY-MM-DD"
              error={!!errors.dateOfBirth}
              style={styles.input}
            />
            <HelperText type="error" visible={!!errors.dateOfBirth}>
              {errors.dateOfBirth}
            </HelperText>
          </View>

          {/* License Information */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Type de permis</Text>
            {LicenseTypes.map(license => (
              <View key={license.value} style={styles.radioItem}>
                <RadioButton
                  value={license.value}
                  status={formData.licenseType === license.value ? 'checked' : 'unchecked'}
                  onPress={() => updateFormData('licenseType', license.value)}
                />
                <Text style={styles.radioLabel}>{license.label}</Text>
              </View>
            ))}
          </View>

          {/* Training Information */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Formation</Text>

            <TextInput
              label="Nombre d'heures de conduite *"
              value={formData.numberOfHours}
              onChangeText={(value) => updateFormData('numberOfHours', value)}
              keyboardType="numeric"
              error={!!errors.numberOfHours}
              style={styles.input}
            />
            <HelperText type="error" visible={!!errors.numberOfHours}>
              {errors.numberOfHours}
            </HelperText>

            <TextInput
              label="Date de début *"
              value={formData.startDate}
              onChangeText={(value) => updateFormData('startDate', value)}
              placeholder="YYYY-MM-DD"
              error={!!errors.startDate}
              style={styles.input}
            />
            <HelperText type="error" visible={!!errors.startDate}>
              {errors.startDate}
            </HelperText>
          </View>

          {/* Emergency Contact */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Contact d'urgence</Text>

            <TextInput
              label="Nom du contact d'urgence"
              value={formData.emergencyContact}
              onChangeText={(value) => updateFormData('emergencyContact', value)}
              style={styles.input}
            />

            <TextInput
              label="Téléphone d'urgence"
              value={formData.emergencyPhone}
              onChangeText={(value) => updateFormData('emergencyPhone', value)}
              keyboardType="phone-pad"
              error={!!errors.emergencyPhone}
              style={styles.input}
            />
            <HelperText type="error" visible={!!errors.emergencyPhone}>
              {errors.emergencyPhone}
            </HelperText>
          </View>

          {/* Additional Information */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Informations supplémentaires</Text>

            <TextInput
              label="Conditions médicales"
              value={formData.medicalConditions}
              onChangeText={(value) => updateFormData('medicalConditions', value)}
              multiline
              numberOfLines={3}
              style={styles.input}
              placeholder="Allergies, conditions médicales, handicaps..."
            />

            <TextInput
              label="Notes"
              value={formData.notes}
              onChangeText={(value) => updateFormData('notes', value)}
              multiline
              numberOfLines={3}
              style={styles.input}
              placeholder="Notes supplémentaires..."
            />
          </View>

          {/* Status */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Statut initial</Text>
            {LearnerStatuses.map(status => (
              <View key={status.value} style={styles.radioItem}>
                <RadioButton
                  value={status.value}
                  status={formData.status === status.value ? 'checked' : 'unchecked'}
                  onPress={() => updateFormData('status', status.value)}
                />
                <Text style={styles.radioLabel}>{status.label}</Text>
              </View>
            ))}
          </View>
        </Card.Content>

        <Card.Actions style={styles.actions}>
          <Button
            mode="outlined"
            onPress={() => router.back()}
            disabled={creating}
          >
            Annuler
          </Button>
          <Button
            mode="contained"
            onPress={handleSubmit}
            loading={creating}
            disabled={creating}
          >
            Créer l'apprenant
          </Button>
        </Card.Actions>
      </Card>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollContainer: {
    flex: 1,
    padding: 16,
  },
  card: {
    marginBottom: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 24,
    textAlign: 'center',
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
    color: '#333',
  },
  input: {
    marginBottom: 8,
  },
  radioItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  radioLabel: {
    marginLeft: 8,
    fontSize: 16,
    flex: 1,
  },
  actions: {
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
});

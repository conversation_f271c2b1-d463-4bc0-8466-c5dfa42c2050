import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView, Alert, SafeAreaView, KeyboardAvoidingView, Platform } from 'react-native';
import {
  Text,
  Card,
  Title,
  Button,
  TextInput,
  HelperText,
  RadioButton,
  ActivityIndicator
} from 'react-native-paper';
import { router } from 'expo-router';
import { useMutation, useQuery } from '@apollo/client';
import { CREATE_LEARNER, REGISTER_USER, UPLOAD_DOCUMENT } from '../../services/graphql/mutations';
import { GET_LEARNERS, GET_CURRENT_USER } from '../../services/graphql/queries';
import AsyncStorage from '@react-native-async-storage/async-storage';
import DocumentUpload from '../../components/learner/DocumentUpload';

const LicenseTypes = [
  { value: 'PERMIS_A1', label: 'Permis A1 (Moto légère)' },
  { value: 'PERMIS_A', label: 'Permis A (Moto)' },
  { value: 'PERMIS_B', label: 'Permis B (Voiture)' },
  { value: 'PERMIS_BE', label: 'Permis BE (Voiture + remorque)' },
  { value: 'PERMIS_C', label: 'Permis C (Poids lourd)' },
  { value: 'PERMIS_CE', label: 'Permis CE (Poids lourd + remorque)' },
  { value: 'PERMIS_D', label: 'Permis D (Transport en commun)' },
  { value: 'PERMIS_D1', label: 'Permis D1 (Minibus)' },
  { value: 'PERMIS_DE', label: 'Permis DE (Bus + remorque)' },
  { value: 'PERMIS_H', label: 'Permis H (Véhicule handicapé)' }
];

const LearnerStatuses = [
  { value: 'INSCRIT', label: 'Inscrit' },
  { value: 'EN_COURS', label: 'En cours' },
  { value: 'TERMINE', label: 'Terminé' },
  { value: 'SUSPENDU', label: 'Suspendu' }
];

export default function CreateLearnerScreen() {
  const [userRole, setUserRole] = useState(null);
  const [isCheckingAuth, setIsCheckingAuth] = useState(true);

  // Vérifier l'autorisation de l'utilisateur
  useEffect(() => {
    const checkUserRole = async () => {
      try {
        const userData = await AsyncStorage.getItem('user');
        if (userData) {
          const user = JSON.parse(userData);
          setUserRole(user.role);
        }
      } catch (error) {
        console.error('Error checking user role:', error);
      } finally {
        setIsCheckingAuth(false);
      }
    };

    checkUserRole();
  }, []);

  const [formData, setFormData] = useState({
    // User information
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    address: '',

    // Learner specific information
    dateOfBirth: '',
    licenseType: 'PERMIS_B',
    cin: '',
    numberOfHours: '20',
    startDate: new Date().toISOString().split('T')[0],
    status: 'INSCRIT',

    // Additional information
    emergencyContact: '',
    emergencyPhone: '',
    medicalConditions: '',
    notes: '',

    // Documents
    documents: {
      cin: null,
      medicalCertificate: null,
      photo: null
    }
  });

  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const [registerUser] = useMutation(REGISTER_USER, {
    onError: (error) => {
      Alert.alert(
        'Erreur lors de la création de l\'utilisateur',
        error.message || 'Une erreur est survenue lors de la création de l\'utilisateur'
      );
    }
  });

  const [createLearner, { loading: creating }] = useMutation(CREATE_LEARNER, {
    refetchQueries: [{ query: GET_LEARNERS }],
    onError: (error) => {
      Alert.alert(
        'Erreur',
        error.message || 'Une erreur est survenue lors de la création'
      );
    }
  });

  const [uploadDocument] = useMutation(UPLOAD_DOCUMENT, {
    onError: (error) => {
      console.error('Error uploading document:', error);
    }
  });

  const validateForm = () => {
    const newErrors = {};

    // User information validation
    if (!formData.firstName.trim()) {
      newErrors.firstName = 'Le prénom est requis';
    }

    if (!formData.lastName.trim()) {
      newErrors.lastName = 'Le nom est requis';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'L\'email est requis';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Format d\'email invalide';
    }

    if (!formData.phone.trim()) {
      newErrors.phone = 'Le numéro de téléphone est requis';
    } else if (!/^\d{8}$/.test(formData.phone.replace(/\s/g, ''))) {
      newErrors.phone = 'Le numéro doit contenir 8 chiffres';
    }

    // Learner specific validation
    if (!formData.dateOfBirth) {
      newErrors.dateOfBirth = 'La date de naissance est requise';
    } else {
      const birthDate = new Date(formData.dateOfBirth);
      const today = new Date();
      const age = today.getFullYear() - birthDate.getFullYear();
      if (age < 16) {
        newErrors.dateOfBirth = 'L\'apprenant doit avoir au moins 16 ans';
      }
      if (age > 80) {
        newErrors.dateOfBirth = 'Âge maximum dépassé';
      }
    }

    if (!formData.cin.trim()) {
      newErrors.cin = 'Le numéro CIN est requis';
    } else if (!/^\d{8}$/.test(formData.cin)) {
      newErrors.cin = 'Le numéro CIN doit contenir exactement 8 chiffres';
    }

    if (!formData.numberOfHours || parseInt(formData.numberOfHours) < 1) {
      newErrors.numberOfHours = 'Le nombre d\'heures doit être supérieur à 0';
    } else if (parseInt(formData.numberOfHours) > 100) {
      newErrors.numberOfHours = 'Le nombre d\'heures ne peut pas dépasser 100';
    }

    if (!formData.startDate) {
      newErrors.startDate = 'La date de début est requise';
    } else {
      const startDate = new Date(formData.startDate);
      const today = new Date();
      if (startDate > new Date(today.getTime() + 30 * 24 * 60 * 60 * 1000)) {
        newErrors.startDate = 'La date de début ne peut pas être dans plus de 30 jours';
      }
    }

    // Emergency contact validation
    if (formData.emergencyContact.trim() && !formData.emergencyPhone.trim()) {
      newErrors.emergencyPhone = 'Le numéro d\'urgence est requis si un contact d\'urgence est spécifié';
    }

    if (formData.emergencyPhone.trim() && !/^\d{8}$/.test(formData.emergencyPhone.replace(/\s/g, ''))) {
      newErrors.emergencyPhone = 'Le numéro d\'urgence doit contenir 8 chiffres';
    }

    // Documents validation
    if (!formData.documents.cin) {
      newErrors.cinDocument = 'La photo de la CIN est requise';
    }

    if (!formData.documents.medicalCertificate) {
      newErrors.medicalCertificateDocument = 'Le certificat médical est requis';
    }

    if (!formData.documents.photo) {
      newErrors.photoDocument = 'La photo d\'identité est requise';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    try {
      console.log('Starting user creation with data:', {
        email: formData.email,
        firstName: formData.firstName,
        lastName: formData.lastName,
        phone: formData.phone,
        address: formData.address,
        role: 'APPRENANT'
      });

      // First create the user
      const userResult = await registerUser({
        variables: {
          input: {
            email: formData.email,
            password: 'temp123456', // Temporary password - should be changed by user
            firstName: formData.firstName,
            lastName: formData.lastName,
            phone: formData.phone,
            address: formData.address,
            role: 'APPRENANT'
          }
        }
      });

      console.log('User creation result:', userResult);

      if (userResult.data?.register?.user) {
        console.log('Creating learner with user ID:', userResult.data.register.user.id);

        // Then create the learner with the user ID
        const learnerResult = await createLearner({
          variables: {
            input: {
              userId: userResult.data.register.user.id,
              dateOfBirth: formData.dateOfBirth,
              licenseType: formData.licenseType,
              cin: formData.cin,
              numberOfHours: parseInt(formData.numberOfHours),
              startDate: formData.startDate,
              status: formData.status,
              emergencyContact: formData.emergencyContact,
              emergencyPhone: formData.emergencyPhone,
              medicalConditions: formData.medicalConditions,
              notes: formData.notes
            }
          }
        });

        if (learnerResult.data?.createLearner) {
          const learnerId = learnerResult.data.createLearner.id;
          console.log('Learner created with ID:', learnerId);

          // Upload documents
          const documentTypes = ['cin', 'medicalCertificate', 'photo'];
          const uploadPromises = documentTypes.map(async (docType) => {
            const document = formData.documents[docType];
            if (document && document.base64) {
              try {
                await uploadDocument({
                  variables: {
                    learnerId: learnerId,
                    documentType: docType,
                    file: document.base64
                  }
                });
                console.log(`Document ${docType} uploaded successfully`);
              } catch (error) {
                console.error(`Error uploading ${docType}:`, error);
                // Continue with other documents even if one fails
              }
            }
          });

          // Wait for all document uploads to complete
          await Promise.allSettled(uploadPromises);

          Alert.alert(
            'Succès',
            'L\'apprenant et ses documents ont été créés avec succès',
            [
              {
                text: 'OK',
                onPress: () => router.push('/learners')
              }
            ]
          );
        } else {
          throw new Error('Échec de la création de l\'apprenant');
        }
      } else {
        throw new Error('Échec de la création de l\'utilisateur');
      }
    } catch (error) {
      console.error('Error creating learner:', error);
      Alert.alert(
        'Erreur',
        error.message || 'Une erreur est survenue lors de la création de l\'apprenant'
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const updateFormData = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: null }));
    }
  };

  // Gérer la sélection de documents
  const handleDocumentSelected = (documentType, document) => {
    setFormData(prev => ({
      ...prev,
      documents: {
        ...prev.documents,
        [documentType]: document
      }
    }));

    // Clear document error
    const errorKey = `${documentType}Document`;
    if (errors[errorKey]) {
      setErrors(prev => ({ ...prev, [errorKey]: null }));
    }
  };

  // Affichage pendant la vérification d'autorisation
  if (isCheckingAuth) {
    return (
      <View style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" />
          <Text style={styles.loadingText}>Vérification des autorisations...</Text>
        </View>
      </View>
    );
  }

  // Message drôle pour les apprenants qui essaient d'accéder à cette page
  if (userRole === 'APPRENANT') {
    return (
      <View style={styles.container}>
        <ScrollView style={styles.scrollView} contentContainerStyle={styles.contentContainer}>
          <View style={styles.funnyErrorContainer}>
          <Card style={styles.funnyCard}>
            <Card.Content>
              <View style={styles.emojiContainer}>
                <Text style={styles.bigEmoji}>🙋‍♂️➡️👨‍💼</Text>
              </View>
              <Title style={styles.funnyTitle}>
                Promotion non autorisée ! 😅
              </Title>
              <Text style={styles.funnyText}>
                Alors alors... On essaie de se promouvoir secrétaire ? 🕵️‍♂️
              </Text>
              <Text style={styles.funnyText}>
                Désolé, mais tu ne peux pas créer d'autres apprenants !
                C'est comme essayer de se donner son propre diplôme ! 🎓
              </Text>
              <Text style={styles.funnySubtext}>
                Cette page est réservée aux administrateurs et secrétaires.
                Toi, tu es déjà un apprenant parfait ! 😊
              </Text>

              <View style={styles.reasonsContainer}>
                <Text style={styles.reasonsTitle}>Pourquoi cette restriction ? 🤔</Text>
                <Text style={styles.reasonText}>• Éviter la création de comptes fantômes 👻</Text>
                <Text style={styles.reasonText}>• Maintenir l'ordre dans la base de données 📊</Text>
                <Text style={styles.reasonText}>• Empêcher les inscriptions frauduleuses 🚫</Text>
                <Text style={styles.reasonText}>• Garder le contrôle administratif 🎯</Text>
              </View>

              <View style={styles.suggestionContainer}>
                <Text style={styles.suggestionTitle}>Ce que tu peux faire à la place : 💡</Text>
                <Text style={styles.suggestionText}>• Gérer ton profil personnel 👤</Text>
                <Text style={styles.suggestionText}>• Télécharger tes documents 📄</Text>
                <Text style={styles.suggestionText}>• Suivre tes cours 📚</Text>
                <Text style={styles.suggestionText}>• Contacter le secrétariat pour ajouter quelqu'un 📞</Text>
              </View>

              <View style={styles.jokeContainer}>
                <Text style={styles.jokeText}>
                  💡 <Text style={styles.jokeEmphasis}>Conseil d'ami :</Text> Si tu veux vraiment créer des apprenants,
                  postule pour un poste de secrétaire ! 😉
                </Text>
              </View>
            </Card.Content>
            <Card.Actions style={styles.funnyActions}>
              <Button
                mode="outlined"
                onPress={() => router.back()}
                icon="arrow-left"
              >
                Retour
              </Button>
              <Button
                mode="contained"
                onPress={() => router.push('/')}
                icon="home"
              >
                Accueil
              </Button>
            </Card.Actions>
          </Card>
          </View>
        </ScrollView>
      </View>
    );
  }

  // Message pour les utilisateurs non connectés
  if (!userRole) {
    return (
      <View style={styles.container}>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>
            Vous devez être connecté en tant qu'administrateur ou secrétaire pour accéder à cette page.
          </Text>
          <Button
            mode="contained"
            onPress={() => router.push('/admin-login')}
            icon="login"
          >
            Se connecter
          </Button>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.contentContainer}
        showsVerticalScrollIndicator={true}
        keyboardShouldPersistTaps="handled"
      >
          <Card style={styles.card}>
          <Card.Content>
          <Title style={styles.title}>Créer un nouvel apprenant</Title>

            {/* Boutons d'action en haut */}
            <View style={styles.topActions}>
              <Button
                mode="outlined"
                onPress={() => router.back()}
                disabled={isSubmitting || creating}
                icon="arrow-left"
                style={styles.topButton}
              >
                Annuler
              </Button>
              <Button
                mode="contained"
                onPress={handleSubmit}
                loading={isSubmitting || creating}
                disabled={isSubmitting || creating}
                icon="check"
                style={styles.topButton}
              >
                Créer l'apprenant
              </Button>
            </View>

          {/* Personal Information */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Informations personnelles</Text>

            <TextInput
              label="Prénom *"
              value={formData.firstName}
              onChangeText={(value) => updateFormData('firstName', value)}
              error={!!errors.firstName}
              style={styles.input}
            />
            <HelperText type="error" visible={!!errors.firstName}>
              {errors.firstName}
            </HelperText>

            <TextInput
              label="Nom *"
              value={formData.lastName}
              onChangeText={(value) => updateFormData('lastName', value)}
              error={!!errors.lastName}
              style={styles.input}
            />
            <HelperText type="error" visible={!!errors.lastName}>
              {errors.lastName}
            </HelperText>

            <TextInput
              label="Email *"
              value={formData.email}
              onChangeText={(value) => updateFormData('email', value)}
              keyboardType="email-address"
              autoCapitalize="none"
              error={!!errors.email}
              style={styles.input}
            />
            <HelperText type="error" visible={!!errors.email}>
              {errors.email}
            </HelperText>

            <TextInput
              label="Téléphone *"
              value={formData.phone}
              onChangeText={(value) => updateFormData('phone', value)}
              keyboardType="phone-pad"
              error={!!errors.phone}
              style={styles.input}
            />
            <HelperText type="error" visible={!!errors.phone}>
              {errors.phone}
            </HelperText>

            <TextInput
              label="Adresse"
              value={formData.address}
              onChangeText={(value) => updateFormData('address', value)}
              multiline
              numberOfLines={2}
              style={styles.input}
            />
          </View>

          {/* Learner Specific Information */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Informations de l'apprenant</Text>

            <TextInput
              label="Numéro CIN *"
              value={formData.cin}
              onChangeText={(value) => updateFormData('cin', value)}
              keyboardType="numeric"
              maxLength={8}
              error={!!errors.cin}
              style={styles.input}
            />
            <HelperText type="error" visible={!!errors.cin}>
              {errors.cin}
            </HelperText>

            <TextInput
              label="Date de naissance *"
              value={formData.dateOfBirth}
              onChangeText={(value) => updateFormData('dateOfBirth', value)}
              placeholder="YYYY-MM-DD"
              error={!!errors.dateOfBirth}
              style={styles.input}
            />
            <HelperText type="error" visible={!!errors.dateOfBirth}>
              {errors.dateOfBirth}
            </HelperText>
          </View>

          {/* License Information */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Type de permis</Text>
            {LicenseTypes.map(license => (
              <View key={license.value} style={styles.radioItem}>
                <RadioButton
                  value={license.value}
                  status={formData.licenseType === license.value ? 'checked' : 'unchecked'}
                  onPress={() => updateFormData('licenseType', license.value)}
                />
                <Text style={styles.radioLabel}>{license.label}</Text>
              </View>
            ))}
          </View>

          {/* Training Information */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Formation</Text>

            <TextInput
              label="Nombre d'heures de conduite *"
              value={formData.numberOfHours}
              onChangeText={(value) => updateFormData('numberOfHours', value)}
              keyboardType="numeric"
              error={!!errors.numberOfHours}
              style={styles.input}
            />
            <HelperText type="error" visible={!!errors.numberOfHours}>
              {errors.numberOfHours}
            </HelperText>

            <TextInput
              label="Date de début *"
              value={formData.startDate}
              onChangeText={(value) => updateFormData('startDate', value)}
              placeholder="YYYY-MM-DD"
              error={!!errors.startDate}
              style={styles.input}
            />
            <HelperText type="error" visible={!!errors.startDate}>
              {errors.startDate}
            </HelperText>
          </View>

          {/* Emergency Contact */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Contact d'urgence</Text>

            <TextInput
              label="Nom du contact d'urgence"
              value={formData.emergencyContact}
              onChangeText={(value) => updateFormData('emergencyContact', value)}
              style={styles.input}
            />

            <TextInput
              label="Téléphone d'urgence"
              value={formData.emergencyPhone}
              onChangeText={(value) => updateFormData('emergencyPhone', value)}
              keyboardType="phone-pad"
              error={!!errors.emergencyPhone}
              style={styles.input}
            />
            <HelperText type="error" visible={!!errors.emergencyPhone}>
              {errors.emergencyPhone}
            </HelperText>
          </View>

          {/* Additional Information */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Informations supplémentaires</Text>

            <TextInput
              label="Conditions médicales"
              value={formData.medicalConditions}
              onChangeText={(value) => updateFormData('medicalConditions', value)}
              multiline
              numberOfLines={3}
              style={styles.input}
              placeholder="Allergies, conditions médicales, handicaps..."
            />

            <TextInput
              label="Notes"
              value={formData.notes}
              onChangeText={(value) => updateFormData('notes', value)}
              multiline
              numberOfLines={3}
              style={styles.input}
              placeholder="Notes supplémentaires..."
            />
          </View>

          {/* Documents requis */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Documents requis</Text>
            <Text style={styles.sectionDescription}>
              Téléchargez les documents nécessaires pour compléter le dossier de l'apprenant.
            </Text>

            {/* CIN */}
            <DocumentUpload
              documentType="cin"
              onDocumentSelected={handleDocumentSelected}
              selectedDocument={formData.documents.cin}
              required={true}
            />
            {errors.cinDocument && (
              <Text style={styles.documentErrorText}>{errors.cinDocument}</Text>
            )}

            {/* Certificat médical */}
            <DocumentUpload
              documentType="medicalCertificate"
              onDocumentSelected={handleDocumentSelected}
              selectedDocument={formData.documents.medicalCertificate}
              required={true}
            />
            {errors.medicalCertificateDocument && (
              <Text style={styles.documentErrorText}>{errors.medicalCertificateDocument}</Text>
            )}

            {/* Photo d'identité */}
            <DocumentUpload
              documentType="photo"
              onDocumentSelected={handleDocumentSelected}
              selectedDocument={formData.documents.photo}
              required={true}
            />
            {errors.photoDocument && (
              <Text style={styles.documentErrorText}>{errors.photoDocument}</Text>
            )}
          </View>

          {/* Status */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Statut initial</Text>
            {LearnerStatuses.map(status => (
              <View key={status.value} style={styles.radioItem}>
                <RadioButton
                  value={status.value}
                  status={formData.status === status.value ? 'checked' : 'unchecked'}
                  onPress={() => updateFormData('status', status.value)}
                />
                <Text style={styles.radioLabel}>{status.label}</Text>
              </View>
            ))}
          </View>
        </Card.Content>

        <Card.Actions style={styles.actions}>
          <Button
            mode="outlined"
            onPress={() => router.back()}
            disabled={isSubmitting || creating}
          >
            Annuler
          </Button>
          <Button
            mode="contained"
            onPress={handleSubmit}
            loading={isSubmitting || creating}
            disabled={isSubmitting || creating}
          >
            Créer l'apprenant
          </Button>
        </Card.Actions>
        </Card>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollView: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
    paddingBottom: 50,
  },
  card: {
    marginBottom: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 16,
    textAlign: 'center',
  },
  topActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 24,
    gap: 12,
  },
  topButton: {
    flex: 1,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
    color: '#333',
  },
  sectionDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 16,
    lineHeight: 20,
  },
  documentErrorText: {
    fontSize: 12,
    color: '#F44336',
    marginTop: 4,
    marginBottom: 8,
  },
  input: {
    marginBottom: 8,
  },
  radioItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  radioLabel: {
    marginLeft: 8,
    fontSize: 16,
    flex: 1,
  },
  actions: {
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
  },

  photoInfoContainer: {
    backgroundColor: '#e3f2fd',
    padding: 12,
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: '#2196F3',
  },
  photoInfoText: {
    fontSize: 14,
    color: '#1976D2',
    marginBottom: 8,
    fontWeight: '500',
  },
  photoInfoSubtext: {
    fontSize: 12,
    color: '#1565C0',
    fontStyle: 'italic',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  errorText: {
    fontSize: 16,
    color: '#F44336',
    marginBottom: 16,
    textAlign: 'center',
  },
  funnyErrorContainer: {
    flex: 1,
    padding: 16,
    justifyContent: 'center',
  },
  funnyCard: {
    elevation: 8,
    backgroundColor: '#fff3e0',
    borderWidth: 2,
    borderColor: '#ff9800',
  },
  emojiContainer: {
    alignItems: 'center',
    marginBottom: 16,
  },
  bigEmoji: {
    fontSize: 64,
  },
  funnyTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    color: '#e65100',
    marginBottom: 16,
  },
  funnyText: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 12,
    color: '#bf360c',
    fontWeight: '500',
  },
  funnySubtext: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 20,
    color: '#5d4037',
    fontStyle: 'italic',
    lineHeight: 20,
  },
  reasonsContainer: {
    backgroundColor: '#ffecb3',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
    borderLeftWidth: 4,
    borderLeftColor: '#ff9800',
  },
  reasonsTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#e65100',
    marginBottom: 8,
  },
  reasonText: {
    fontSize: 14,
    color: '#bf360c',
    marginBottom: 4,
  },
  suggestionContainer: {
    backgroundColor: '#e8f5e8',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
    borderLeftWidth: 4,
    borderLeftColor: '#4caf50',
  },
  suggestionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2e7d32',
    marginBottom: 8,
  },
  suggestionText: {
    fontSize: 14,
    color: '#388e3c',
    marginBottom: 4,
  },
  jokeContainer: {
    backgroundColor: '#f3e5f5',
    padding: 16,
    borderRadius: 8,
    marginBottom: 8,
    borderLeftWidth: 4,
    borderLeftColor: '#9c27b0',
  },
  jokeText: {
    fontSize: 14,
    color: '#6a1b9a',
    textAlign: 'center',
    lineHeight: 20,
  },
  jokeEmphasis: {
    fontWeight: 'bold',
    color: '#4a148c',
  },
  funnyActions: {
    justifyContent: 'space-between',
    paddingHorizontal: 16,
  },
});

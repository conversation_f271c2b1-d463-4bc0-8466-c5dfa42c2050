const Learner = require('../../models/learner');
const User = require('../../models/user');
const Session = require('../../models/session');
const Payment = require('../../models/payment');
const LearnerCourseProgress = require('../../models/learnerCourseProgress');
const Exam = require('../../models/exam');
const { AuthenticationError, UserInputError } = require('apollo-server-express');

const learnerResolvers = {
  Query: {
    // Get all learners
    learners: async (_, __, { req }) => {
      try {
        // Check if user is authenticated and is admin or secretary
        if (!req.user || (req.user.role !== 'ADMINISTRATEUR' && req.user.role !== 'SECRETAIRE')) {
          throw new AuthenticationError('Not authorized to access this resource');
        }

        return await Learner.find({});
      } catch (error) {
        throw new Error(error.message);
      }
    },

    // Get learner by ID
    learner: async (_, { id }, { req }) => {
      try {
        // Check if user is authenticated
        if (!req.user) {
          throw new AuthenticationError('Not authenticated');
        }

        const learner = await Learner.findById(id);

        if (!learner) {
          throw new Error('Learner not found');
        }

        // Check if user is admin, instructor or the learner themselves
        if (req.user.role !== 'ADMINISTRATEUR' &&
            req.user.role !== 'INSTRUCTEUR' &&
            req.user.id !== learner.user.toString()) {
          throw new AuthenticationError('Not authorized to access this resource');
        }

        return learner;
      } catch (error) {
        throw new Error(error.message);
      }
    },

    // Get learner by user ID
    learnerByUserId: async (_, { userId }, { req }) => {
      try {
        // Check if user is authenticated
        if (!req.user) {
          throw new AuthenticationError('Not authenticated');
        }

        // Check if user is admin, instructor or the user themselves
        if (req.user.role !== 'ADMINISTRATEUR' &&
            req.user.role !== 'INSTRUCTEUR' &&
            req.user.id !== userId) {
          throw new AuthenticationError('Not authorized to access this resource');
        }

        return await Learner.findOne({ user: userId });
      } catch (error) {
        throw new Error(error.message);
      }
    },

    // Get learner progress
    learnerProgress: async (_, { id }, { req }) => {
      try {
        // Check if user is authenticated
        if (!req.user) {
          throw new AuthenticationError('Not authenticated');
        }

        const learner = await Learner.findById(id);

        if (!learner) {
          throw new Error('Learner not found');
        }

        // Check if user is admin, instructor or the learner themselves
        if (req.user.role !== 'ADMINISTRATEUR' &&
            req.user.role !== 'INSTRUCTEUR' &&
            req.user.id !== learner.user.toString()) {
          throw new AuthenticationError('Not authorized to access this resource');
        }

        return learner.progress;
      } catch (error) {
        throw new Error(error.message);
      }
    },

    // Get learner sessions
    learnerSessions: async (_, { id }, { req }) => {
      try {
        // Check if user is authenticated
        if (!req.user) {
          throw new AuthenticationError('Not authenticated');
        }

        const learner = await Learner.findById(id);

        if (!learner) {
          throw new Error('Learner not found');
        }

        // Check if user is admin, instructor or the learner themselves
        if (req.user.role !== 'ADMINISTRATEUR' &&
            req.user.role !== 'INSTRUCTEUR' &&
            req.user.id !== learner.user.toString()) {
          throw new AuthenticationError('Not authorized to access this resource');
        }

        return await Session.find({ learner: id });
      } catch (error) {
        throw new Error(error.message);
      }
    },

    // Get learner payments
    learnerPayments: async (_, { id }, { req }) => {
      try {
        // Check if user is authenticated
        if (!req.user) {
          throw new AuthenticationError('Not authenticated');
        }

        const learner = await Learner.findById(id);

        if (!learner) {
          throw new Error('Learner not found');
        }

        // Check if user is admin or the learner themselves
        if (req.user.role !== 'ADMINISTRATEUR' &&
            req.user.id !== learner.user.toString()) {
          throw new AuthenticationError('Not authorized to access this resource');
        }

        return await Payment.find({ learner: id });
      } catch (error) {
        throw new Error(error.message);
      }
    },

    // Get learners with pending documents
    learnersWithPendingDocuments: async (_, __, { req }) => {
      try {
        // Check if user is authenticated and is admin or secretary
        if (!req.user || (req.user.role !== 'ADMINISTRATEUR' && req.user.role !== 'SECRETAIRE')) {
          throw new AuthenticationError('Not authorized to access this resource');
        }

        return await Learner.find({
          $or: [
            { 'documents.cin.status': 'EN_ATTENTE' },
            { 'documents.medicalCertificate.status': 'EN_ATTENTE' },
            { 'documents.photo.status': 'EN_ATTENTE' }
          ]
        });
      } catch (error) {
        throw new Error(error.message);
      }
    },

    // Get learner documents status
    learnerDocumentsStatus: async (_, { id }, { req }) => {
      try {
        // Check if user is authenticated
        if (!req.user) {
          throw new AuthenticationError('Not authenticated');
        }

        const learner = await Learner.findById(id);

        if (!learner) {
          throw new Error('Learner not found');
        }

        // Check if user is admin, secretary, instructor or the learner themselves
        if (req.user.role !== 'ADMINISTRATEUR' &&
            req.user.role !== 'SECRETAIRE' &&
            req.user.role !== 'INSTRUCTEUR' &&
            req.user.id !== learner.user.toString()) {
          throw new AuthenticationError('Not authorized to access this resource');
        }

        return learner.documents;
      } catch (error) {
        throw new Error(error.message);
      }
    },

    // Get learner fees
    learnerFees: async (_, { id }, { req }) => {
      try {
        // Check if user is authenticated
        if (!req.user) {
          throw new AuthenticationError('Not authenticated');
        }

        const learner = await Learner.findById(id);

        if (!learner) {
          throw new Error('Learner not found');
        }

        // Check if user is admin, secretary or the learner themselves
        if (req.user.role !== 'ADMINISTRATEUR' &&
            req.user.role !== 'SECRETAIRE' &&
            req.user.id !== learner.user.toString()) {
          throw new AuthenticationError('Not authorized to access this resource');
        }

        return learner.fees;
      } catch (error) {
        throw new Error(error.message);
      }
    }
  },

  Mutation: {
    // Create a new learner
    createLearner: async (_, { input }, { req }) => {
      try {
        // Check if user is authenticated and is admin
        if (!req.user || req.user.role !== 'ADMINISTRATEUR') {
          throw new AuthenticationError('Not authorized to access this resource');
        }

        // Generate unique ID
        const id = `LEARNER-${Date.now()}`;

        // Create learner
        const learner = await Learner.create({
          id,
          ...input
        });

        return learner;
      } catch (error) {
        throw new Error(error.message);
      }
    },

    // Update learner
    updateLearner: async (_, { id, input }, { req }) => {
      try {
        // Check if user is authenticated and is admin
        if (!req.user || req.user.role !== 'ADMINISTRATEUR') {
          throw new AuthenticationError('Not authorized to access this resource');
        }

        const learner = await Learner.findById(id);

        if (!learner) {
          throw new Error('Learner not found');
        }

        // Update learner fields
        Object.keys(input).forEach(key => {
          if (input[key] !== undefined) {
            learner[key] = input[key];
          }
        });

        // Save updated learner
        const updatedLearner = await learner.save();

        return updatedLearner;
      } catch (error) {
        throw new Error(error.message);
      }
    },

    // Delete learner
    deleteLearner: async (_, { id }, { req }) => {
      try {
        // Check if user is authenticated and is admin
        if (!req.user || req.user.role !== 'ADMINISTRATEUR') {
          throw new AuthenticationError('Not authorized to access this resource');
        }

        const learner = await Learner.findById(id);

        if (!learner) {
          throw new Error('Learner not found');
        }

        await learner.remove();

        return true;
      } catch (error) {
        throw new Error(error.message);
        return false;
      }
    },

    // Validate document
    validateDocument: async (_, { input }, { req }) => {
      try {
        // Check if user is authenticated and is admin or secretary
        if (!req.user || (req.user.role !== 'ADMINISTRATEUR' && req.user.role !== 'SECRETAIRE')) {
          throw new AuthenticationError('Not authorized to access this resource');
        }

        const learner = await Learner.findById(input.learnerId);

        if (!learner) {
          throw new Error('Learner not found');
        }

        // Validate document type
        if (!['cin', 'medicalCertificate', 'photo'].includes(input.documentType)) {
          throw new UserInputError('Invalid document type');
        }

        // Update document status
        learner.documents[input.documentType].status = input.status;
        learner.documents[input.documentType].comment = input.comment || '';
        learner.documents[input.documentType].validatedAt = new Date();
        learner.documents[input.documentType].validatedBy = req.user.id;

        const updatedLearner = await learner.save();
        return updatedLearner;
      } catch (error) {
        throw new Error(error.message);
      }
    },

    // Upload document
    uploadDocument: async (_, { learnerId, documentType, file }, { req }) => {
      try {
        // Check if user is authenticated
        if (!req.user) {
          throw new AuthenticationError('Not authenticated');
        }

        const learner = await Learner.findById(learnerId);

        if (!learner) {
          throw new Error('Learner not found');
        }

        // Check if user is admin, secretary or the learner themselves
        if (req.user.role !== 'ADMINISTRATEUR' &&
            req.user.role !== 'SECRETAIRE' &&
            req.user.id !== learner.user.toString()) {
          throw new AuthenticationError('Not authorized to access this resource');
        }

        // Validate document type
        if (!['cin', 'medicalCertificate', 'photo'].includes(documentType)) {
          throw new UserInputError('Invalid document type');
        }

        // Update document file and reset status to pending
        learner.documents[documentType].file = file;
        learner.documents[documentType].status = 'EN_ATTENTE';
        learner.documents[documentType].uploadedAt = new Date();
        learner.documents[documentType].comment = '';
        learner.documents[documentType].validatedAt = null;
        learner.documents[documentType].validatedBy = null;

        const updatedLearner = await learner.save();
        return updatedLearner;
      } catch (error) {
        throw new Error(error.message);
      }
    },

    // Update learner fees
    updateLearnerFees: async (_, { input }, { req }) => {
      try {
        // Check if user is authenticated and is admin or secretary
        if (!req.user || (req.user.role !== 'ADMINISTRATEUR' && req.user.role !== 'SECRETAIRE')) {
          throw new AuthenticationError('Not authorized to access this resource');
        }

        const learner = await Learner.findById(input.learnerId);

        if (!learner) {
          throw new Error('Learner not found');
        }

        // Update fees
        if (input.drivingHours !== undefined) learner.fees.drivingHours = input.drivingHours;
        if (input.theoryLessons !== undefined) learner.fees.theoryLessons = input.theoryLessons;
        if (input.codeExam !== undefined) learner.fees.codeExam = input.codeExam;
        if (input.drivingExam !== undefined) learner.fees.drivingExam = input.drivingExam;
        if (input.parkingExam !== undefined) learner.fees.parkingExam = input.parkingExam;

        // Fees will be automatically calculated by the pre-save middleware
        const updatedLearner = await learner.save();
        return updatedLearner;
      } catch (error) {
        throw new Error(error.message);
      }
    },

    // Update learner progress
    updateLearnerProgress: async (_, { input }, { req }) => {
      try {
        // Check if user is authenticated and is instructor, admin or secretary
        if (!req.user || (req.user.role !== 'ADMINISTRATEUR' &&
                          req.user.role !== 'SECRETAIRE' &&
                          req.user.role !== 'INSTRUCTEUR' &&
                          req.user.role !== 'MONITEUR')) {
          throw new AuthenticationError('Not authorized to access this resource');
        }

        const learner = await Learner.findById(input.learnerId);

        if (!learner) {
          throw new Error('Learner not found');
        }

        // Update theory progress
        if (input.theoryPercentage !== undefined) {
          learner.progressDetails.theory.percentage = input.theoryPercentage;
        }
        if (input.theoryLessonsCompleted !== undefined) {
          learner.progressDetails.theory.lessonsCompleted = input.theoryLessonsCompleted;
        }
        if (input.theoryTotalLessons !== undefined) {
          learner.progressDetails.theory.totalLessons = input.theoryTotalLessons;
        }

        // Update driving progress
        if (input.drivingPercentage !== undefined) {
          learner.progressDetails.driving.percentage = input.drivingPercentage;
        }
        if (input.drivingHoursCompleted !== undefined) {
          learner.progressDetails.driving.hoursCompleted = input.drivingHoursCompleted;
        }
        if (input.drivingTotalHours !== undefined) {
          learner.progressDetails.driving.totalHours = input.drivingTotalHours;
        }

        // Progress will be automatically calculated by the pre-save middleware
        const updatedLearner = await learner.save();
        return updatedLearner;
      } catch (error) {
        throw new Error(error.message);
      }
    },

    // Calculate learner fees
    calculateLearnerFees: async (_, { id }, { req }) => {
      try {
        // Check if user is authenticated and is admin or secretary
        if (!req.user || (req.user.role !== 'ADMINISTRATEUR' && req.user.role !== 'SECRETAIRE')) {
          throw new AuthenticationError('Not authorized to access this resource');
        }

        const learner = await Learner.findById(id);

        if (!learner) {
          throw new Error('Learner not found');
        }

        // Recalculate fees
        learner.calculateFees();
        await learner.save();

        return learner.fees;
      } catch (error) {
        throw new Error(error.message);
      }
    }
  },

  Learner: {
    // Resolve user field
    user: async (parent) => {
      try {
        return await User.findById(parent.user);
      } catch (error) {
        throw new Error(error.message);
      }
    },

    // Resolve sessions field
    sessions: async (parent) => {
      try {
        return await Session.find({ learner: parent._id });
      } catch (error) {
        throw new Error(error.message);
      }
    },

    // Resolve payments field
    payments: async (parent) => {
      try {
        return await Payment.find({ learner: parent._id });
      } catch (error) {
        throw new Error(error.message);
      }
    },

    // Resolve courseProgress field
    courseProgress: async (parent) => {
      try {
        return await LearnerCourseProgress.find({ learner: parent._id });
      } catch (error) {
        throw new Error(error.message);
      }
    },

    // Resolve exams field
    exams: async (parent) => {
      try {
        return await Exam.find({ learner: parent._id });
      } catch (error) {
        throw new Error(error.message);
      }
    }
  },

  // Resolvers pour les nouveaux types
  LearnerDocuments: {
    cin: (parent) => parent.cin,
    medicalCertificate: (parent) => parent.medicalCertificate,
    photo: (parent) => parent.photo
  },

  DocumentInfo: {
    validatedBy: async (parent) => {
      if (parent.validatedBy) {
        try {
          return await User.findById(parent.validatedBy);
        } catch (error) {
          return null;
        }
      }
      return null;
    }
  },

  LearnerFees: {
    drivingHours: (parent) => parent.drivingHours || 0,
    theoryLessons: (parent) => parent.theoryLessons || 0,
    codeExam: (parent) => parent.codeExam || 0,
    drivingExam: (parent) => parent.drivingExam || 0,
    parkingExam: (parent) => parent.parkingExam || 0,
    total: (parent) => parent.total || 0,
    paid: (parent) => parent.paid || 0,
    remaining: (parent) => parent.remaining || 0
  },

  LearnerProgressDetails: {
    theory: (parent) => parent.theory,
    driving: (parent) => parent.driving
  },

  ProgressInfo: {
    percentage: (parent) => parent.percentage || 0,
    lessonsCompleted: (parent) => parent.lessonsCompleted || 0,
    totalLessons: (parent) => parent.totalLessons || 0,
    hoursCompleted: (parent) => parent.hoursCompleted || 0,
    totalHours: (parent) => parent.totalHours || 0
  }
};

module.exports = learnerResolvers;

{"version": 3, "file": "ImagePicker.types.js", "sourceRoot": "", "sources": ["../src/ImagePicker.types.ts"], "names": [], "mappings": "AAsBA,cAAc;AACd;;GAEG;AACH,MAAM,CAAN,IAAY,gBAaX;AAbD,WAAY,gBAAgB;IAC1B;;OAEG;IACH,+BAAW,CAAA;IACX;;OAEG;IACH,qCAAiB,CAAA;IACjB;;OAEG;IACH,qCAAiB,CAAA;AACnB,CAAC,EAbW,gBAAgB,KAAhB,gBAAgB,QAa3B;AA0BD,cAAc;AACd,MAAM,CAAN,IAAY,iBAmEX;AAnED,WAAY,iBAAiB;IAC3B;;;;OAIG;IACH,uEAAe,CAAA;IACf;;;;OAIG;IACH,qEAAc,CAAA;IACd;;;;OAIG;IACH,2EAAiB,CAAA;IACjB;;;;OAIG;IACH,6EAAkB,CAAA;IAClB;;;;OAIG;IACH,yEAAgB,CAAA;IAChB;;;;OAIG;IACH,yEAAgB,CAAA;IAChB;;;;OAIG;IACH,2EAAiB,CAAA;IACjB;;;;OAIG;IACH,6EAAkB,CAAA;IAClB;;;;OAIG;IACH,6EAAkB,CAAA;IAClB;;;;OAIG;IACH,6EAAkB,CAAA;IAClB;;;;OAIG;IACH,8EAAmB,CAAA;AACrB,CAAC,EAnEW,iBAAiB,KAAjB,iBAAiB,QAmE5B;AAED,cAAc;AACd,MAAM,CAAN,IAAY,kCAyBX;AAzBD,WAAY,kCAAkC;IAC5C;;OAEG;IACH,2FAAQ,CAAA;IACR;;OAEG;IACH,+FAAU,CAAA;IACV;;OAEG;IACH,yFAAO,CAAA;IACP;;OAEG;IACH,uGAAc,CAAA;IACd;;OAEG;IACH,+GAAkB,CAAA;IAClB;;OAEG;IACH,6GAAiB,CAAA;AACnB,CAAC,EAzBW,kCAAkC,KAAlC,kCAAkC,QAyB7C;AAED;;;;GAIG;AACH,MAAM,CAAN,IAAY,8BAoCX;AApCD,WAAY,8BAA8B;IACxC;;OAEG;IACH,4DAA0B,CAAA;IAC1B;;OAEG;IACH,0DAAwB,CAAA;IACxB;;OAEG;IACH,0DAAwB,CAAA;IACxB;;OAEG;IACH,oEAAkC,CAAA;IAClC;;OAEG;IACH,qEAAmC,CAAA;IACnC;;OAEG;IACH,6EAA2C,CAAA;IAC3C;;OAEG;IACH,qDAAmB,CAAA;IACnB;;;;;OAKG;IACH,yDAAuB,CAAA;AACzB,CAAC,EApCW,8BAA8B,KAA9B,8BAA8B,QAoCzC;AAED;;;;GAIG;AACH,MAAM,CAAN,IAAY,6CAaX;AAbD,WAAY,6CAA6C;IACvD;;OAEG;IACH,wEAAuB,CAAA;IACvB;;OAEG;IACH,0EAAyB,CAAA;IACzB;;OAEG;IACH,oEAAmB,CAAA;AACrB,CAAC,EAbW,6CAA6C,KAA7C,6CAA6C,QAaxD;AAED,MAAM,CAAN,IAAY,UASX;AATD,WAAY,UAAU;IACpB;;OAEG;IACH,2BAAa,CAAA;IACb;;OAEG;IACH,6BAAe,CAAA;AACjB,CAAC,EATW,UAAU,KAAV,UAAU,QASrB", "sourcesContent": ["import { PermissionResponse } from 'expo-modules-core';\n\n// @needsAudit\n/**\n * Alias for `PermissionResponse` type exported by `expo-modules-core`.\n */\nexport type CameraPermissionResponse = PermissionResponse;\n\n// @needsAudit\n/**\n * Extends `PermissionResponse` type exported by `expo-modules-core`, containing additional iOS-specific field.\n */\nexport type MediaLibraryPermissionResponse = PermissionResponse & {\n  /**\n   * Indicates if your app has access to the whole or only part of the photo library. Possible values are:\n   * - `'all'` if the user granted your app access to the whole photo library\n   * - `'limited'` if the user granted your app access only to selected photos (only available on Android API 34+ and iOS 14.0+)\n   * - `'none'` if user denied or hasn't yet granted the permission\n   */\n  accessPrivileges?: 'all' | 'limited' | 'none';\n};\n\n// @needsAudit\n/**\n * @deprecated To set media types available in the image picker use an array of [`MediaType`](#mediatype) instead.\n */\nexport enum MediaTypeOptions {\n  /**\n   * Images and videos.\n   */\n  All = 'All',\n  /**\n   * Only videos.\n   */\n  Videos = 'Videos',\n  /**\n   * Only images.\n   */\n  Images = 'Images',\n}\n\n/**\n * Media types that can be picked by the image picker.\n * - `'images'` - for images.\n * - `'videos'` - for videos.\n * - `'livePhotos'` - for live photos (iOS only).\n *\n * > When the `livePhotos` type is added to the media types array and a live photo is selected,\n * > the resulting `ImagePickerAsset` will contain an unaltered image and the `pairedVideoAsset` field will contain a\n * > video asset paired with the image. This option will be ignored when the `allowsEditing` option is enabled. Due\n * > to platform limitations live photos are returned at original quality, regardless of the `quality` option.\n *\n * > When on Android or Web `livePhotos` type passed as a media type will be ignored.\n */\nexport type MediaType = 'images' | 'videos' | 'livePhotos';\n\n/**\n * The default tab with which the image picker will be opened.\n * - `'photos'` - the photos/videos tab will be opened.\n * - `'albums'` - the albums tab will be opened.\n *\n * @platform android\n */\nexport type DefaultTab = 'photos' | 'albums';\n\n// @needsAudit\nexport enum VideoExportPreset {\n  /**\n   * Resolution: __Unchanged__ •\n   * Video compression: __None__ •\n   * Audio compression: __None__\n   */\n  Passthrough = 0,\n  /**\n   * Resolution: __Depends on the device__ •\n   * Video compression: __H.264__ •\n   * Audio compression: __AAC__\n   */\n  LowQuality = 1,\n  /**\n   * Resolution: __Depends on the device__ •\n   * Video compression: __H.264__ •\n   * Audio compression: __AAC__\n   */\n  MediumQuality = 2,\n  /**\n   * Resolution: __Depends on the device__ •\n   * Video compression: __H.264__ •\n   * Audio compression: __AAC__\n   */\n  HighestQuality = 3,\n  /**\n   * Resolution: __640 × 480__ •\n   * Video compression: __H.264__ •\n   * Audio compression: __AAC__\n   */\n  H264_640x480 = 4,\n  /**\n   * Resolution: __960 × 540__ •\n   * Video compression: __H.264__ •\n   * Audio compression: __AAC__\n   */\n  H264_960x540 = 5,\n  /**\n   * Resolution: __1280 × 720__ •\n   * Video compression: __H.264__ •\n   * Audio compression: __AAC__\n   */\n  H264_1280x720 = 6,\n  /**\n   * Resolution: __1920 × 1080__ •\n   * Video compression: __H.264__ •\n   * Audio compression: __AAC__\n   */\n  H264_1920x1080 = 7,\n  /**\n   * Resolution: __3840 × 2160__ •\n   * Video compression: __H.264__ •\n   * Audio compression: __AAC__\n   */\n  H264_3840x2160 = 8,\n  /**\n   * Resolution: __1920 × 1080__ •\n   * Video compression: __HEVC__ •\n   * Audio compression: __AAC__\n   */\n  HEVC_1920x1080 = 9,\n  /**\n   * Resolution: __3840 × 2160__ •\n   * Video compression: __HEVC__ •\n   * Audio compression: __AAC__\n   */\n  HEVC_3840x2160 = 10,\n}\n\n// @needsAudit\nexport enum UIImagePickerControllerQualityType {\n  /**\n   * Highest available resolution.\n   */\n  High = 0,\n  /**\n   * Depends on the device.\n   */\n  Medium = 1,\n  /**\n   * Depends on the device.\n   */\n  Low = 2,\n  /**\n   * 640 × 480\n   */\n  VGA640x480 = 3,\n  /**\n   * 1280 × 720\n   */\n  IFrame1280x720 = 4,\n  /**\n   * 960 × 540\n   */\n  IFrame960x540 = 5,\n}\n\n/**\n * Picker presentation style. Its values are directly mapped to the [`UIModalPresentationStyle`](https://developer.apple.com/documentation/uikit/uiviewcontroller/1621355-modalpresentationstyle).\n *\n * @platform ios\n */\nexport enum UIImagePickerPresentationStyle {\n  /**\n   * A presentation style in which the presented picker covers the screen.\n   */\n  FULL_SCREEN = 'fullScreen',\n  /**\n   * A presentation style that partially covers the underlying content.\n   */\n  PAGE_SHEET = 'pageSheet',\n  /**\n   * A presentation style that displays the picker centered in the screen.\n   */\n  FORM_SHEET = 'formSheet',\n  /**\n   * A presentation style where the picker is displayed over the app's content.\n   */\n  CURRENT_CONTEXT = 'currentContext',\n  /**\n   * A presentation style in which the picker view covers the screen.\n   */\n  OVER_FULL_SCREEN = 'overFullScreen',\n  /**\n   * A presentation style where the picker is displayed over the app's content.\n   */\n  OVER_CURRENT_CONTEXT = 'overCurrentContext',\n  /**\n   * A presentation style where the picker is displayed in a popover view.\n   */\n  POPOVER = 'popover',\n  /**\n   * The default presentation style chosen by the system.\n   * On older iOS versions, falls back to `WebBrowserPresentationStyle.FullScreen`.\n   *\n   * @platform ios\n   */\n  AUTOMATIC = 'automatic',\n}\n\n/**\n * Picker preferred asset representation mode. Its values are directly mapped to the [`PHPickerConfigurationAssetRepresentationMode`](https://developer.apple.com/documentation/photokit/phpickerconfigurationassetrepresentationmode).\n *\n * @platform ios\n */\nexport enum UIImagePickerPreferredAssetRepresentationMode {\n  /**\n   * A mode that indicates that the system chooses the appropriate asset representation.\n   */\n  Automatic = 'automatic',\n  /**\n   * A mode that uses the most compatible asset representation.\n   */\n  Compatible = 'compatible',\n  /**\n   * A mode that uses the current representation to avoid transcoding, if possible.\n   */\n  Current = 'current',\n}\n\nexport enum CameraType {\n  /**\n   * Back/rear camera.\n   */\n  back = 'back',\n  /**\n   * Front camera\n   */\n  front = 'front',\n}\n\n/**\n * @hidden\n * @deprecated Use `ImagePickerAsset` instead\n */\nexport type ImageInfo = ImagePickerAsset;\n\n/**\n * Represents an asset (image or video) returned by the image picker or camera.\n */\nexport type ImagePickerAsset = {\n  /**\n   * URI to the local image or video file (usable as the source of an `Image` element, in the case of\n   * an image) and `width` and `height` specify the dimensions of the media.\n   */\n  uri: string;\n  /**\n   * The unique ID that represents the picked image or video, if picked from the library. It can be used\n   * by [expo-media-library](./media-library) to manage the picked asset.\n   *\n   * > This might be `null` when the ID is unavailable or the user gave limited permission to access the media library.\n   * > On Android, the ID is unavailable when the user selects a photo by directly browsing file system.\n   *\n   * @platform android\n   * @platform ios\n   */\n  assetId?: string | null;\n  /**\n   * Width of the image or video.\n   */\n  width: number;\n  /**\n   * Height of the image or video.\n   */\n  height: number;\n  /**\n   * The type of the asset.\n   * - `'image'` - for images.\n   * - `'video'` - for videos.\n   * - `'livePhoto'` - for live photos. (iOS only)\n   * - `'pairedVideo'` - for videos paired with photos, which can be combined to create a live photo. (iOS only)\n   */\n  type?: 'image' | 'video' | 'livePhoto' | 'pairedVideo';\n  /**\n   * Preferred filename to use when saving this item. This might be `null` when the name is unavailable\n   * or user gave limited permission to access the media library.\n   *\n   */\n  fileName?: string | null;\n  /**\n   * File size of the picked image or video, in bytes.\n   *\n   */\n  fileSize?: number;\n  /**\n   * The `exif` field is included if the `exif` option is truthy, and is an object containing the\n   * image's EXIF data. The names of this object's properties are EXIF tags and the values are the\n   * respective EXIF values for those tags.\n   *\n   * @platform android\n   * @platform ios\n   */\n  exif?: Record<string, any> | null;\n  /**\n   * When the `base64` option is truthy, it is a Base64-encoded string of the selected image's JPEG data, otherwise `null`.\n   * If you prepend this with `'data:image/jpeg;base64,'` to create a data URI,\n   * you can use it as the source of an `Image` element; for example:\n   * ```ts\n   * <Image\n   *   source={{ uri: 'data:image/jpeg;base64,' + asset.base64 }}\n   *   style={{ width: 200, height: 200 }}\n   * />\n   * ```\n   */\n  base64?: string | null;\n  /**\n   * Length of the video in milliseconds or `null` if the asset is not a video.\n   */\n  duration?: number | null;\n  /**\n   * The MIME type of the selected asset or `null` if could not be determined.\n   */\n  mimeType?: string;\n  /**\n   * Contains information about the video paired with the image file. This property is only set when `livePhotos` media type was present in the `mediaTypes` array when launching the picker and a live photo was selected.\n   *\n   * @platform ios\n   */\n  pairedVideoAsset?: ImagePickerAsset | null;\n\n  /**\n   * The web `File` object containing the selected media. This property is web-only and can be used to upload to a server with `FormData`.\n   *\n   * @platform web\n   */\n  file?: File;\n};\n\n// @needsAudit\nexport type ImagePickerErrorResult = {\n  /**\n   * The error code.\n   */\n  code: string;\n  /**\n   * The error message.\n   */\n  message: string;\n  /**\n   * The exception which caused the error.\n   */\n  exception?: string;\n};\n\n// @needsAudit\n/**\n * Type representing successful and canceled pick result.\n */\nexport type ImagePickerResult = ImagePickerSuccessResult | ImagePickerCanceledResult;\n\n/**\n * Type representing successful pick result.\n */\nexport type ImagePickerSuccessResult = {\n  /**\n   * Boolean flag set to `false` showing that the request was successful.\n   */\n  canceled: false;\n  /**\n   * An array of picked assets.\n   */\n  assets: ImagePickerAsset[];\n};\n\n/**\n * Type representing canceled pick result.\n */\nexport type ImagePickerCanceledResult = {\n  /**\n   * Boolean flag set to `true` showing that the request was canceled.\n   */\n  canceled: true;\n  /**\n   * `null` signifying that the request was canceled.\n   */\n  assets: null;\n};\n\n/**\n * @hidden\n * @deprecated Use `ImagePickerResult` instead.\n */\nexport type ImagePickerCancelledResult = ImagePickerCanceledResult;\n\n/**\n * @hidden\n * @deprecated `ImagePickerMultipleResult` has been deprecated in favor of `ImagePickerResult`.\n */\nexport type ImagePickerMultipleResult = ImagePickerResult;\n\n// @needsAudit\nexport type ImagePickerOptions = {\n  /**\n   * Whether to show a UI to edit the image after it is picked. On Android the user can crop and\n   * rotate the image and on iOS simply crop it.\n   *\n   * > - Cropping multiple images is not supported - this option is mutually exclusive with `allowsMultipleSelection`.\n   * > - On iOS, this option is ignored if `allowsMultipleSelection` is enabled.\n   * > - On iOS cropping a `.bmp` image will convert it to `.png`.\n   *\n   * @default false\n   * @platform android\n   * @platform ios\n   */\n  allowsEditing?: boolean;\n  /**\n   * An array with two entries `[x, y]` specifying the aspect ratio to maintain if the user is\n   * allowed to edit the image (by passing `allowsEditing: true`). This is only applicable on\n   * Android, since on iOS the crop rectangle is always a square.\n   */\n  aspect?: [number, number];\n  /**\n   * Specify the quality of compression, from `0` to `1`. `0` means compress for small size,\n   * `1` means compress for maximum quality.\n   * > Note: If the selected image has been compressed before, the size of the output file may be\n   * > bigger than the size of the original image.\n   *\n   * > Note: On iOS, if a `.bmp` or `.png` image is selected from the library, this option is ignored.\n   *\n   * @default 1.0\n   * @platform android\n   * @platform ios\n   */\n  quality?: number;\n  /**\n   * Choose what type of media to pick.\n   * @default 'images'\n   */\n  mediaTypes?: MediaType | MediaType[] | MediaTypeOptions;\n  /**\n   * Whether to also include the EXIF data for the image. On iOS the EXIF data does not include GPS\n   * tags in the camera case.\n   *\n   * @platform android\n   * @platform ios\n   */\n  exif?: boolean;\n  /**\n   * Whether to also include the image data in Base64 format.\n   */\n  base64?: boolean;\n  /**\n   * Specify preset which will be used to compress selected video.\n   * @default ImagePicker.VideoExportPreset.Passthrough\n   * @platform ios 11+\n   * @deprecated See [`videoExportPreset`](https://developer.apple.com/documentation/uikit/uiimagepickercontroller/2890964-videoexportpreset?language=objc)\n   * in Apple documentation.\n   */\n  videoExportPreset?: VideoExportPreset;\n  /**\n   * Specify the quality of recorded videos. Defaults to the highest quality available for the device.\n   * @default ImagePicker.UIImagePickerControllerQualityType.High\n   * @platform ios\n   */\n  videoQuality?: UIImagePickerControllerQualityType;\n  /**\n   * Whether or not to allow selecting multiple media files at once.\n   *\n   * > Cropping multiple images is not supported - this option is mutually exclusive with `allowsEditing`.\n   * > If this option is enabled, then `allowsEditing` is ignored.\n   *\n   * @default false\n   * @platform android\n   * @platform ios 14+\n   * @platform web\n   */\n  allowsMultipleSelection?: boolean;\n  /**\n   * The maximum number of items that user can select. Applicable when `allowsMultipleSelection` is enabled.\n   * Setting the value to `0` sets the selection limit to the maximum that the system supports.\n   *\n   * @platform android\n   * @platform ios 14+\n   * @default 0\n   */\n  selectionLimit?: number;\n  /**\n   * Whether to display number badges when assets are selected. The badges are numbered\n   * in selection order. Assets are then returned in the exact same order they were selected.\n   *\n   * > Assets should be returned in the selection order regardless of this option,\n   * > but there is no guarantee that it is always true when this option is disabled.\n   *\n   * @platform ios 15+\n   * @default false\n   */\n  orderedSelection?: boolean;\n  /**\n   * Choose the default tab with which the image picker will be opened.\n   * @default 'photos'\n   * @platform android\n   */\n  defaultTab?: DefaultTab;\n  /**\n   * Maximum duration, in seconds, for video recording. Setting this to `0` disables the limit.\n   * Defaults to `0` (no limit).\n   * - **On iOS**, when `allowsEditing` is set to `true`, maximum duration is limited to 10 minutes.\n   *   This limit is applied automatically, if `0` or no value is specified.\n   * - **On Android**, effect of this option depends on support of installed camera app.\n   * - **On Web** this option has no effect - the limit is browser-dependant.\n   */\n  videoMaxDuration?: number;\n  /**\n   * Choose [presentation style](https://developer.apple.com/documentation/uikit/uiviewcontroller/1621355-modalpresentationstyle?language=objc)\n   * to customize view during taking photo/video.\n   * @default ImagePicker.UIImagePickerPresentationStyle.Automatic\n   * @platform ios\n   */\n  presentationStyle?: UIImagePickerPresentationStyle;\n  /**\n   * Selects the camera-facing type. The `CameraType` enum provides two options:\n   * `front` for the front-facing camera and `back` for the back-facing camera.\n   * - **On Android**, the behavior of this option may vary based on the camera app installed on the device.\n   * - **On Web**, if this option is not provided, use \"camera\" as the default value of internal input element for backwards compatibility.\n   * @default CameraType.back\n   */\n  cameraType?: CameraType;\n  /**\n   * Choose [preferred asset representation mode](https://developer.apple.com/documentation/photokit/phpickerconfigurationassetrepresentationmode)\n   * to use when loading assets.\n   * @default ImagePicker.UIImagePickerPreferredAssetRepresentationMode.Automatic\n   * @platform ios 14+\n   */\n  preferredAssetRepresentationMode?: UIImagePickerPreferredAssetRepresentationMode;\n  /**\n   * Uses the legacy image picker on Android. This will allow media to be selected from outside the users photo library.\n   * @platform android\n   * @default false\n   */\n  legacy?: boolean;\n};\n\n/**\n * @hidden\n * @deprecated Only used internally.\n */\nexport type OpenFileBrowserOptions = {\n  /**\n   * Choose what type of media to pick.\n   * @default 'images'\n   */\n  mediaTypes: MediaType | MediaType[] | MediaTypeOptions;\n  // @docsMissing\n  capture?: boolean | CameraType;\n  /**\n   * Whether or not to allow selecting multiple media files at once.\n   * @platform web\n   */\n  allowsMultipleSelection: boolean;\n  /**\n   * Whether to also include the image data in Base64 format.\n   */\n  base64: boolean;\n};\n\n/**\n * @hidden\n * @deprecated Use `ImagePickerResult` or `OpenFileBrowserOptions` instead.\n */\nexport type ExpandImagePickerResult<T extends ImagePickerOptions | OpenFileBrowserOptions> =\n  T extends {\n    allowsMultipleSelection: true;\n  }\n    ? ImagePickerResult\n    : ImagePickerResult;\n"]}
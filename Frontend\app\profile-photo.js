import React, { useState } from 'react';
import { View, StyleSheet, Alert, ScrollView } from 'react-native';
import {
  Text,
  Surface,
  Button,
  Avatar,
  ActivityIndicator,
  Card,
  Title,
  Paragraph,
  Divider,
  Appbar
} from 'react-native-paper';
import { useQuery, useMutation } from '@apollo/client';
import { GET_CURRENT_USER } from '../services/graphql/queries';
import { UPDATE_USER_PROFILE } from '../services/graphql/mutations';
import { useAuth } from '../contexts/AuthContext';
import { router } from 'expo-router';
import * as ImagePicker from 'expo-image-picker';

export default function ProfilePhotoScreen() {
  const { user } = useAuth();
  const [uploading, setUploading] = useState(false);

  // Get current user data
  const { loading, error, data, refetch } = useQuery(GET_CURRENT_USER);

  // Update profile mutation
  const [updateProfile, { loading: updateLoading }] = useMutation(UPDATE_USER_PROFILE);

  const userData = data?.me || user;

  // Request permissions
  const requestPermissions = async () => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert(
        'Permission requise',
        'Nous avons besoin de la permission d\'accéder à vos photos pour que vous puissiez changer votre photo de profil.',
        [{ text: 'OK' }]
      );
      return false;
    }
    return true;
  };

  // Pick image from gallery
  const pickImageFromGallery = async () => {
    const hasPermission = await requestPermissions();
    if (!hasPermission) return;

    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
        base64: true,
      });

      if (!result.canceled && result.assets[0]) {
        await uploadPhoto(result.assets[0]);
      }
    } catch (error) {
      console.error('Error picking image:', error);
      Alert.alert('Erreur', 'Impossible de sélectionner l\'image');
    }
  };

  // Take photo with camera
  const takePhoto = async () => {
    const { status } = await ImagePicker.requestCameraPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert(
        'Permission requise',
        'Nous avons besoin de la permission d\'accéder à votre caméra.',
        [{ text: 'OK' }]
      );
      return;
    }

    try {
      const result = await ImagePicker.launchCameraAsync({
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
        base64: true,
      });

      if (!result.canceled && result.assets[0]) {
        await uploadPhoto(result.assets[0]);
      }
    } catch (error) {
      console.error('Error taking photo:', error);
      Alert.alert('Erreur', 'Impossible de prendre la photo');
    }
  };

  // Upload photo
  const uploadPhoto = async (imageAsset) => {
    setUploading(true);
    try {
      // Convert to base64 data URL
      const base64Image = `data:image/jpeg;base64,${imageAsset.base64}`;

      await updateProfile({
        variables: {
          input: {
            photo: base64Image
          }
        }
      });

      // Refetch user data
      await refetch();

      Alert.alert(
        'Succès',
        'Votre photo de profil a été mise à jour !',
        [
          {
            text: 'OK',
            onPress: () => router.back()
          }
        ]
      );
    } catch (error) {
      console.error('Error uploading photo:', error);
      let errorMessage = 'Impossible de mettre à jour la photo';
      if (error.message.includes('Cast to ObjectId failed')) {
        errorMessage = 'Erreur de compte utilisateur. Veuillez vous reconnecter.';
      } else if (error.message) {
        errorMessage += ': ' + error.message;
      }
      Alert.alert('Erreur', errorMessage);
    } finally {
      setUploading(false);
    }
  };

  // Remove photo
  const removePhoto = async () => {
    Alert.alert(
      'Supprimer la photo',
      'Êtes-vous sûr de vouloir supprimer votre photo de profil ?',
      [
        { text: 'Annuler', style: 'cancel' },
        {
          text: 'Supprimer',
          style: 'destructive',
          onPress: async () => {
            try {
              await updateProfile({
                variables: {
                  input: {
                    photo: null
                  }
                }
              });
              await refetch();
              Alert.alert('Succès', 'Photo supprimée');
            } catch (error) {
              Alert.alert('Erreur', 'Impossible de supprimer la photo');
            }
          }
        }
      ]
    );
  };

  if (loading) {
    return (
      <View style={styles.container}>
        <Appbar.Header style={styles.header}>
          <Appbar.BackAction onPress={() => router.back()} iconColor="#fff" />
          <Appbar.Content title="Photo de profil" titleStyle={styles.headerTitle} />
        </Appbar.Header>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" />
          <Text style={styles.loadingText}>Chargement...</Text>
        </View>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.container}>
        <Appbar.Header style={styles.header}>
          <Appbar.BackAction onPress={() => router.back()} iconColor="#fff" />
          <Appbar.Content title="Photo de profil" titleStyle={styles.headerTitle} />
        </Appbar.Header>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Erreur de chargement</Text>
          <Button mode="contained" onPress={() => router.back()}>
            Retour
          </Button>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Header */}
      <Appbar.Header style={styles.header}>
        <Appbar.BackAction onPress={() => router.back()} iconColor="#fff" />
        <Appbar.Content title="Photo de profil" titleStyle={styles.headerTitle} />
      </Appbar.Header>

      <ScrollView style={styles.scrollContainer}>
        {/* Current Photo */}
        <Card style={styles.photoCard}>
          <Card.Content style={styles.photoContent}>
            <View style={styles.avatarContainer}>
              {userData?.photo ? (
                <Avatar.Image
                  size={150}
                  source={{ uri: userData.photo }}
                  style={styles.avatar}
                />
              ) : (
                <Avatar.Text
                  size={150}
                  label={userData ? `${userData.firstName?.charAt(0) || ''}${userData.lastName?.charAt(0) || ''}` : '?'}
                  style={styles.avatar}
                />
              )}
            </View>

            <Text style={styles.userName}>
              {userData?.firstName} {userData?.lastName}
            </Text>

            <Text style={styles.userRole}>
              {userData?.role === 'APPRENANT' ? 'Apprenant' : userData?.role}
            </Text>
          </Card.Content>
        </Card>

        {/* Actions */}
        <Card style={styles.actionsCard}>
          <Card.Content>
            <Title>Changer la photo</Title>
            <Paragraph style={styles.description}>
              Choisissez une nouvelle photo de profil depuis votre galerie ou prenez-en une avec votre caméra.
            </Paragraph>

            <Divider style={styles.divider} />

            <View style={styles.buttonContainer}>
              <Button
                mode="contained"
                onPress={pickImageFromGallery}
                icon="image"
                style={styles.actionButton}
                disabled={uploading || updateLoading}
              >
                Galerie
              </Button>

              <Button
                mode="contained"
                onPress={takePhoto}
                icon="camera"
                style={styles.actionButton}
                disabled={uploading || updateLoading}
              >
                Caméra
              </Button>
            </View>

            {userData?.photo && (
              <>
                <Divider style={styles.divider} />
                <Button
                  mode="outlined"
                  onPress={removePhoto}
                  icon="delete"
                  style={styles.removeButton}
                  disabled={uploading || updateLoading}
                  textColor="#F44336"
                >
                  Supprimer la photo
                </Button>
              </>
            )}

            {(uploading || updateLoading) && (
              <View style={styles.uploadingContainer}>
                <ActivityIndicator size="small" />
                <Text style={styles.uploadingText}>
                  {uploading ? 'Téléchargement...' : 'Mise à jour...'}
                </Text>
              </View>
            )}
          </Card.Content>
        </Card>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#6200ee',
  },
  headerTitle: {
    color: '#fff',
    fontWeight: 'bold',
  },
  scrollContainer: {
    flex: 1,
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  errorText: {
    fontSize: 16,
    color: '#F44336',
    marginBottom: 16,
    textAlign: 'center',
  },
  photoCard: {
    marginBottom: 16,
    elevation: 4,
  },
  photoContent: {
    alignItems: 'center',
    padding: 24,
  },
  avatarContainer: {
    marginBottom: 16,
  },
  avatar: {
    borderWidth: 3,
    borderColor: '#6200ee',
  },
  userName: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  userRole: {
    fontSize: 16,
    color: '#666',
  },
  actionsCard: {
    marginBottom: 16,
    elevation: 4,
  },
  description: {
    marginBottom: 16,
    color: '#666',
  },
  divider: {
    marginVertical: 16,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
  },
  actionButton: {
    flex: 1,
    paddingVertical: 8,
  },
  removeButton: {
    borderColor: '#F44336',
  },
  uploadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 16,
    padding: 16,
    backgroundColor: '#f0f0f0',
    borderRadius: 8,
  },
  uploadingText: {
    marginLeft: 8,
    fontSize: 16,
  },
});

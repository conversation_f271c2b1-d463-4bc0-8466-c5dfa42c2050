import React, { useState } from 'react';
import { View, StyleSheet, ScrollView, Alert } from 'react-native';
import {
  Text,
  Card,
  Button,
  Surface,
  ActivityIndicator
} from 'react-native-paper';
import { useRouter } from 'expo-router';
import { useMutation, useQuery } from '@apollo/client';
import { useAuth } from '../contexts/AuthContext';
import DocumentUpload from '../components/learner/DocumentUpload';
import { UPLOAD_DOCUMENT } from '../services/graphql/mutations';
import { GET_CURRENT_USER } from '../services/graphql/queries';

export default function UploadDocumentsScreen() {
  const router = useRouter();
  const { user } = useAuth();
  const [documents, setDocuments] = useState({
    cin: null,
    medicalCertificate: null,
    photo: null
  });
  const [uploading, setUploading] = useState(false);

  // Get current user data to check if user is learner
  const { loading, error, data } = useQuery(GET_CURRENT_USER);

  const [uploadDocument] = useMutation(UPLOAD_DOCUMENT, {
    refetchQueries: [{ query: GET_CURRENT_USER }],
    onError: (error) => {
      console.error('Error uploading document:', error);
      Alert.alert('Erreur', 'Erreur lors du téléversement du document');
    }
  });

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" />
        <Text style={styles.loadingText}>Chargement...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>Erreur de chargement</Text>
        <Text>{error.message}</Text>
      </View>
    );
  }

  const userData = data?.me || user;

  // Vérifier si l'utilisateur est un apprenant
  if (userData?.role !== 'APPRENANT') {
    return (
      <View style={styles.container}>
        <Card style={styles.errorCard}>
          <Card.Content>
            <Text style={styles.errorTitle}>Accès non autorisé</Text>
            <Text style={styles.errorMessage}>
              Cette page est réservée aux apprenants pour téléverser leurs documents.
            </Text>
            <Button
              mode="contained"
              onPress={() => router.back()}
              style={styles.backButton}
            >
              Retour
            </Button>
          </Card.Content>
        </Card>
      </View>
    );
  }

  // Vérifier si l'apprenant existe
  if (!userData?.learner) {
    return (
      <View style={styles.container}>
        <Card style={styles.errorCard}>
          <Card.Content>
            <Text style={styles.errorTitle}>Profil apprenant non trouvé</Text>
            <Text style={styles.errorMessage}>
              Votre profil d'apprenant n'a pas été trouvé. Contactez l'administration.
            </Text>
            <Button
              mode="contained"
              onPress={() => router.back()}
              style={styles.backButton}
            >
              Retour
            </Button>
          </Card.Content>
        </Card>
      </View>
    );
  }

  const handleDocumentSelected = (documentType, document) => {
    setDocuments(prev => ({
      ...prev,
      [documentType]: document
    }));
  };

  const handleUploadDocument = async (documentType) => {
    const document = documents[documentType];
    if (!document || !document.base64) {
      Alert.alert('Erreur', 'Aucun document sélectionné');
      return;
    }

    setUploading(true);
    try {
      await uploadDocument({
        variables: {
          learnerId: userData.learner.id,
          documentType: documentType,
          file: document.base64
        }
      });

      Alert.alert(
        'Succès',
        'Document téléversé avec succès. Il sera validé par l\'administration.',
        [{ text: 'OK' }]
      );

      // Reset the document after successful upload
      setDocuments(prev => ({
        ...prev,
        [documentType]: null
      }));

    } catch (error) {
      console.error('Error uploading document:', error);
      Alert.alert('Erreur', 'Erreur lors du téléversement du document');
    } finally {
      setUploading(false);
    }
  };

  const handleUploadAll = async () => {
    const documentsToUpload = Object.entries(documents).filter(([_, doc]) => doc && doc.base64);
    
    if (documentsToUpload.length === 0) {
      Alert.alert('Erreur', 'Aucun document à téléverser');
      return;
    }

    setUploading(true);
    try {
      const uploadPromises = documentsToUpload.map(([docType, document]) =>
        uploadDocument({
          variables: {
            learnerId: userData.learner.id,
            documentType: docType,
            file: document.base64
          }
        })
      );

      await Promise.all(uploadPromises);

      Alert.alert(
        'Succès',
        `${documentsToUpload.length} document(s) téléversé(s) avec succès. Ils seront validés par l'administration.`,
        [
          {
            text: 'OK',
            onPress: () => router.push('/profile')
          }
        ]
      );

      // Reset all documents after successful upload
      setDocuments({
        cin: null,
        medicalCertificate: null,
        photo: null
      });

    } catch (error) {
      console.error('Error uploading documents:', error);
      Alert.alert('Erreur', 'Erreur lors du téléversement des documents');
    } finally {
      setUploading(false);
    }
  };

  const hasDocumentsToUpload = Object.values(documents).some(doc => doc && doc.base64);

  return (
    <View style={styles.container}>
      <ScrollView 
        style={styles.scrollView}
        contentContainerStyle={styles.contentContainer}
        showsVerticalScrollIndicator={true}
      >
        <Card style={styles.card}>
          <Card.Content>
            <Text style={styles.title}>📄 Téléverser mes documents</Text>
            <Text style={styles.description}>
              Téléversez vos documents requis pour votre dossier d'inscription. 
              Ils seront validés par l'administration de l'auto-école.
            </Text>

            {/* Instructions */}
            <Surface style={styles.instructionsCard}>
              <Text style={styles.instructionsTitle}>📋 Instructions importantes</Text>
              <Text style={styles.instructionText}>
                • Assurez-vous que vos documents sont lisibles et de bonne qualité
              </Text>
              <Text style={styles.instructionText}>
                • Les photos doivent être nettes et sans reflets
              </Text>
              <Text style={styles.instructionText}>
                • Tous les documents sont obligatoires pour valider votre inscription
              </Text>
              <Text style={styles.instructionText}>
                • Vous recevrez une notification une fois vos documents validés
              </Text>
            </Surface>

            {/* Upload des documents */}
            <DocumentUpload
              documentType="cin"
              onDocumentSelected={handleDocumentSelected}
              selectedDocument={documents.cin}
              required={true}
            />

            <DocumentUpload
              documentType="medicalCertificate"
              onDocumentSelected={handleDocumentSelected}
              selectedDocument={documents.medicalCertificate}
              required={true}
            />

            <DocumentUpload
              documentType="photo"
              onDocumentSelected={handleDocumentSelected}
              selectedDocument={documents.photo}
              required={true}
            />

            {/* Boutons d'action */}
            <View style={styles.actionButtons}>
              <Button
                mode="outlined"
                onPress={() => router.back()}
                disabled={uploading}
                icon="arrow-left"
                style={styles.actionButton}
              >
                Retour
              </Button>
              
              <Button
                mode="contained"
                onPress={handleUploadAll}
                disabled={!hasDocumentsToUpload || uploading}
                loading={uploading}
                icon="cloud-upload"
                style={styles.actionButton}
              >
                Téléverser tout
              </Button>
            </View>

            {/* Statut des documents existants */}
            {userData.learner.documents && (
              <Surface style={styles.statusCard}>
                <Text style={styles.statusTitle}>📊 Statut de vos documents</Text>
                <Text style={styles.statusDescription}>
                  Consultez votre profil pour voir l'état de validation de vos documents déjà téléversés.
                </Text>
                <Button
                  mode="outlined"
                  onPress={() => router.push('/profile')}
                  icon="account"
                  style={styles.profileButton}
                >
                  Voir mon profil
                </Button>
              </Surface>
            )}
          </Card.Content>
        </Card>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollView: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
    paddingBottom: 50,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 18,
    color: '#F44336',
    marginBottom: 10,
    textAlign: 'center',
  },
  card: {
    elevation: 4,
  },
  errorCard: {
    margin: 16,
    elevation: 4,
  },
  errorTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#F44336',
    marginBottom: 12,
    textAlign: 'center',
  },
  errorMessage: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 20,
    lineHeight: 24,
  },
  backButton: {
    marginTop: 10,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 16,
    textAlign: 'center',
    color: '#333',
  },
  description: {
    fontSize: 16,
    color: '#666',
    marginBottom: 20,
    lineHeight: 24,
    textAlign: 'center',
  },
  instructionsCard: {
    padding: 16,
    borderRadius: 8,
    marginBottom: 20,
    backgroundColor: '#e8f4fd',
    borderLeftWidth: 4,
    borderLeftColor: '#2196F3',
  },
  instructionsTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1976D2',
    marginBottom: 12,
  },
  instructionText: {
    fontSize: 14,
    color: '#1976D2',
    marginBottom: 6,
    lineHeight: 20,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
    gap: 12,
  },
  actionButton: {
    flex: 1,
  },
  statusCard: {
    padding: 16,
    borderRadius: 8,
    marginTop: 20,
    backgroundColor: '#f8f9fa',
    borderLeftWidth: 4,
    borderLeftColor: '#4CAF50',
  },
  statusTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2E7D32',
    marginBottom: 8,
  },
  statusDescription: {
    fontSize: 14,
    color: '#2E7D32',
    marginBottom: 12,
    lineHeight: 20,
  },
  profileButton: {
    borderColor: '#4CAF50',
  },
});

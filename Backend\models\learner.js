const mongoose = require('mongoose');

const learnerSchema = new mongoose.Schema({
  id: {
    type: String,
    required: true,
    unique: true
  },
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  dateOfBirth: {
    type: Date,
    required: true
  },
  licenseType: {
    type: String,
    enum: ['PERMIS_A1', 'PERMIS_A', 'PERMIS_B', 'PERMIS_BE', 'PERMIS_C', 'PERMIS_CE', 'PERMIS_D', 'PERMIS_D1', 'PERMIS_DE', 'PERMIS_H'],
    required: true
  },
  medicalCertificate: {
    type: String
  },
  numberOfHours: {
    type: Number,
    default: 0
  },
  startDate: {
    type: Date,
    default: Date.now
  },
  endDate: {
    type: Date
  },
  progress: {
    type: Number,
    default: 0
  },
  status: {
    type: String,
    enum: ['INSCRIT', 'EN_COURS', 'TERMINE', 'SUSPENDU'],
    default: 'INSCRIT'
  },
  // Informations personnelles étendues
  cin: {
    type: String,
    required: true
  },

  // Contact d'urgence
  emergencyContact: {
    type: String,
    trim: true
  },

  emergencyPhone: {
    type: String,
    trim: true
  },

  // Informations médicales et notes
  medicalConditions: {
    type: String,
    trim: true
  },

  notes: {
    type: String,
    trim: true
  },
  // Documents du dossier
  documents: {
    cin: {
      file: String, // Chemin vers le fichier
      status: {
        type: String,
        enum: ['EN_ATTENTE', 'ACCEPTE', 'REJETE'],
        default: 'EN_ATTENTE'
      },
      comment: String, // Commentaire du secrétaire
      uploadedAt: {
        type: Date,
        default: Date.now
      },
      validatedAt: Date,
      validatedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      }
    },
    medicalCertificate: {
      file: String,
      status: {
        type: String,
        enum: ['EN_ATTENTE', 'ACCEPTE', 'REJETE'],
        default: 'EN_ATTENTE'
      },
      comment: String,
      uploadedAt: {
        type: Date,
        default: Date.now
      },
      validatedAt: Date,
      validatedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      }
    },
    photo: {
      file: String,
      status: {
        type: String,
        enum: ['EN_ATTENTE', 'ACCEPTE', 'REJETE'],
        default: 'EN_ATTENTE'
      },
      comment: String,
      uploadedAt: {
        type: Date,
        default: Date.now
      },
      validatedAt: Date,
      validatedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      }
    }
  },
  // Calcul des frais
  fees: {
    drivingHours: {
      type: Number,
      default: 0
    },
    theoryLessons: {
      type: Number,
      default: 0
    },
    codeExam: {
      type: Number,
      default: 0
    },
    drivingExam: {
      type: Number,
      default: 0
    },
    parkingExam: {
      type: Number,
      default: 0
    },
    total: {
      type: Number,
      default: 0
    },
    paid: {
      type: Number,
      default: 0
    },
    remaining: {
      type: Number,
      default: 0
    }
  },
  // Progression détaillée
  progressDetails: {
    theory: {
      percentage: {
        type: Number,
        default: 0
      },
      lessonsCompleted: {
        type: Number,
        default: 0
      },
      totalLessons: {
        type: Number,
        default: 0
      }
    },
    driving: {
      percentage: {
        type: Number,
        default: 0
      },
      hoursCompleted: {
        type: Number,
        default: 0
      },
      totalHours: {
        type: Number,
        default: 20 // Valeur par défaut
      }
    }
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Méthodes pour calculer les frais automatiquement
learnerSchema.methods.calculateFees = function() {
  // Prix par défaut (à configurer selon l'auto-école)
  const prices = {
    drivingHourPrice: 50, // 50 DT par heure de conduite
    theoryLessonPrice: 10, // 10 DT par cours de code
    codeExamPrice: 30, // 30 DT pour l'examen de code
    drivingExamPrice: 50, // 50 DT pour l'examen de conduite
    parkingExamPrice: 25 // 25 DT pour l'examen de parking
  };

  this.fees.total =
    (this.fees.drivingHours * prices.drivingHourPrice) +
    (this.fees.theoryLessons * prices.theoryLessonPrice) +
    (this.fees.codeExam * prices.codeExamPrice) +
    (this.fees.drivingExam * prices.drivingExamPrice) +
    (this.fees.parkingExam * prices.parkingExamPrice);

  this.fees.remaining = this.fees.total - this.fees.paid;

  return this.fees;
};

// Méthode pour calculer la progression globale
learnerSchema.methods.calculateProgress = function() {
  const theoryWeight = 0.3; // 30% pour la théorie
  const drivingWeight = 0.7; // 70% pour la conduite

  this.progress =
    (this.progressDetails.theory.percentage * theoryWeight) +
    (this.progressDetails.driving.percentage * drivingWeight);

  return this.progress;
};

// Méthode pour vérifier si tous les documents sont validés
learnerSchema.methods.areDocumentsValidated = function() {
  return this.documents.cin.status === 'ACCEPTE' &&
         this.documents.medicalCertificate.status === 'ACCEPTE' &&
         this.documents.photo.status === 'ACCEPTE';
};

// Méthode pour obtenir le statut des documents
learnerSchema.methods.getDocumentsStatus = function() {
  const total = 3;
  const validated = [
    this.documents.cin.status,
    this.documents.medicalCertificate.status,
    this.documents.photo.status
  ].filter(status => status === 'ACCEPTE').length;

  return {
    total,
    validated,
    pending: total - validated,
    percentage: (validated / total) * 100
  };
};

// Middleware pour calculer automatiquement les frais et la progression avant la sauvegarde
learnerSchema.pre('save', function(next) {
  this.calculateFees();
  this.calculateProgress();
  next();
});

module.exports = mongoose.model('Learner', learnerSchema);

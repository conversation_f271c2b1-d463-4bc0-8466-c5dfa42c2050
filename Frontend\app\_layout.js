import React, { useEffect } from 'react';
import { DarkTheme, DefaultTheme, ThemeProvider } from '@react-navigation/native';
import { useFonts } from 'expo-font';
import { Stack, useRouter, useSegments } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { ApolloProvider } from '@apollo/client';
import { Provider as PaperProvider, DefaultTheme as PaperDefaultTheme } from 'react-native-paper';
import { AuthProvider, useAuth } from '../contexts/AuthContext';
import client from '../services/graphql/client';
import 'react-native-reanimated';

// Define theme
const paperTheme = {
  ...PaperDefaultTheme,
  colors: {
    ...PaperDefaultTheme.colors,
    primary: '#1976D2',
    accent: '#FF4081',
  },
};

// Auth guard component
function AuthGuard({ children }) {
  const { user, loading } = useAuth();
  const segments = useSegments();
  const router = useRouter();

  // Déterminer si l'utilisateur est authentifié
  const isAuthenticated = !!user;

  useEffect(() => {
    // Ne rien faire pendant le chargement
    if (loading) return;

    console.log('AuthGuard effect running:', {
      isAuthenticated,
      currentSegment: segments[0],
      allSegments: segments,
      user: user ? 'Yes' : 'No'
    });

    const inAuthGroup = segments[0] === '(auth)';
    const inTabsGroup = segments[0] === '(tabs)';

    // Routes qui nécessitent une authentification
    const protectedRoutes = ['learners', 'sessions', 'payments', 'exams', 'vehicles'];
    const isProtectedRoute = protectedRoutes.includes(segments[0]);

    if (!isAuthenticated && !inAuthGroup) {
      // Redirect to login if not authenticated and not already in auth group
      console.log('Redirecting to login - User not authenticated');
      // Utiliser setTimeout pour éviter les problèmes de navigation
      setTimeout(() => {
        router.replace('/login');
      }, 100);
    } else if (isAuthenticated && inAuthGroup) {
      // Redirect to home if authenticated and in auth group
      console.log('Redirecting to home - User authenticated');
      router.replace('/');
    }
  }, [isAuthenticated, loading, segments, router, user]);

  // Afficher un indicateur de chargement pendant le chargement
  if (loading) {
    return null; // Ou un écran de chargement
  }

  return children;
}

export default function RootLayout() {
  const [loaded] = useFonts({
    SpaceMono: require('../assets/fonts/SpaceMono-Regular.ttf'),
  });

  if (!loaded) {
    return null;
  }

  return (
    <ApolloProvider client={client}>
      <PaperProvider theme={paperTheme}>
        <AuthProvider>
          <AuthGuard>
            <ThemeProvider value={DefaultTheme}>
              <Stack>
                <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
                <Stack.Screen name="(auth)" options={{ headerShown: false }} />
                <Stack.Screen
                  name="learners"
                  options={{
                    headerShown: true,
                    title: 'Gestion des apprenants'
                  }}
                />
                <Stack.Screen name="+not-found" />
              </Stack>
              <StatusBar style="auto" />
            </ThemeProvider>
          </AuthGuard>
        </AuthProvider>
      </PaperProvider>
    </ApolloProvider>
  );
}

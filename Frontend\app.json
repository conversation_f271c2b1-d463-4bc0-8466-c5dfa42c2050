{"expo": {"name": "Frontend", "slug": "Frontend", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "frontend", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}], ["expo-image-picker", {"photosPermission": "L'application accède à vos photos pour vous permettre de changer votre photo de profil.", "cameraPermission": "L'application accède à votre caméra pour vous permettre de prendre une photo de profil."}]], "experiments": {"typedRoutes": true}}}
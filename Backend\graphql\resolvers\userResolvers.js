const User = require('../../models/user');
const Learner = require('../../models/learner');
const Employee = require('../../models/employee');
const DrivingSchool = require('../../models/drivingSchool');
const Notification = require('../../models/notification');
const Message = require('../../models/message');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { AuthenticationError, UserInputError } = require('apollo-server-express');

  // Generate JWT token
const generateToken = (user) => {
  console.log('Generating token for user:', {
    id: user.id,
    _id: user._id ? user._id.toString() : null,
    email: user.email,
    role: user.role
  });

  // Utiliser _id si disponible, sinon utiliser id
  const userId = user._id ? user._id.toString() : user.id;

  return jwt.sign(
    { id: userId, email: user.email, role: user.role },
    process.env.JWT_SECRET,
    { expiresIn: process.env.JWT_EXPIRE }
  );
};

const userResolvers = {
  Query: {
    // Get all users
    users: async (_, __, { req }) => {
      try {
        // Check if user is authenticated and is admin
        if (!req.user || req.user.role !== 'ADMINISTRATEUR') {
          throw new AuthenticationError('Not authorized to access this resource');
        }

        return await User.find({});
      } catch (error) {
        throw new Error(error.message);
      }
    },

    // Get user by ID
    user: async (_, { id }, { req }) => {
      try {
        // Check if user is authenticated
        if (!req.user) {
          throw new AuthenticationError('Not authenticated');
        }

        // Check if user is admin or the user themselves
        if (req.user.role !== 'ADMINISTRATEUR' && req.user.id !== id) {
          throw new AuthenticationError('Not authorized to access this resource');
        }

        return await User.findById(id);
      } catch (error) {
        throw new Error(error.message);
      }
    },

    // Get current user
// Dans Backend\graphql\resolvers\userResolvers.js
me: async (_, __, { req }) => {
  try {
    console.log('me resolver called');
    console.log('User in context:', req.user ? req.user.email : 'No user');

    // Check if user is authenticated
    if (!req.user) {
      console.log('Not authenticated in me resolver');
      throw new AuthenticationError('Not authenticated');
    }

    return req.user;
  } catch (error) {
    console.error('me resolver error:', error);
    throw new Error(error.message);
  }
}
  },

  Mutation: {
    // Register a new user
    registerUser: async (_, { input }) => {
      try {
        const { email, password, firstName, lastName, phone, address, role, drivingSchoolId } = input;

        // Check if user already exists
        const userExists = await User.findOne({ email });
        if (userExists) {
          throw new UserInputError('User already exists');
        }

        // Create user (MongoDB will generate the ObjectId automatically)
        const user = await User.create({
          email,
          password,
          firstName,
          lastName,
          phone,
          address,
          role,
          drivingSchool: drivingSchoolId
        });

        // Generate token
        const token = generateToken(user);

        return {
          token,
          user
        };
      } catch (error) {
        throw new Error(error.message);
      }
    },

  // Dans graphql/resolvers/userResolvers.js
loginUser: async (_, { email, password }) => {
  try {
    console.log('Login attempt with email:', email);
    console.log('Password provided:', password);

    // Find user by email
    const user = await User.findOne({ email });
    console.log('User found:', user ? 'Yes' : 'No');

    if (!user) {
      console.log('No user found with this email');
      throw new AuthenticationError('Invalid email or password');
    }

    // Log user details (except password)
    console.log('User details:', {
      id: user._id,
      email: user.email,
      role: user.role,
      passwordLength: user.password ? user.password.length : 0
    });

    // TEMPORAIRE : Contourner la vérification du mot de passe
    // Décommentez la ligne ci-dessous pour contourner la vérification
    const isMatch = true; // Contournement temporaire

    // Commentez la ligne ci-dessous pour contourner la vérification
    // const isMatch = await user.matchPassword(password);

    console.log('Password match (bypassed):', isMatch ? 'Yes' : 'No');

    if (isMatch) {
      // Generate token
      const token = generateToken(user);
      console.log('Token generated successfully');

      return {
        token,
        user
      };
    } else {
      console.log('Password does not match');
      throw new AuthenticationError('Invalid email or password');
    }
  } catch (error) {
    console.error('Login error:', error.message);
    throw new Error(error.message);
  }
},


    // Update user
    updateUser: async (_, { id, input }, { req }) => {
      try {
        // Check if user is authenticated and is admin
        if (!req.user || req.user.role !== 'ADMINISTRATEUR') {
          throw new AuthenticationError('Not authorized to access this resource');
        }

        const user = await User.findById(id);

        if (!user) {
          throw new Error('User not found');
        }

        // Update user fields
        if (input.email) user.email = input.email;
        if (input.firstName) user.firstName = input.firstName;
        if (input.lastName) user.lastName = input.lastName;
        if (input.phone) user.phone = input.phone;
        if (input.address) user.address = input.address;
        if (input.photo) user.photo = input.photo;
        if (input.role) user.role = input.role;
        if (input.drivingSchoolId) user.drivingSchool = input.drivingSchoolId;

        // Update password if provided
        if (input.password) {
          user.password = input.password;
        }

        // Save updated user
        const updatedUser = await user.save();

        return updatedUser;
      } catch (error) {
        throw new Error(error.message);
      }
    },

    // Delete user
    deleteUser: async (_, { id }, { req }) => {
      try {
        // Check if user is authenticated and is admin
        if (!req.user || req.user.role !== 'ADMINISTRATEUR') {
          throw new AuthenticationError('Not authorized to access this resource');
        }

        const user = await User.findById(id);

        if (!user) {
          throw new Error('User not found');
        }

        await user.remove();

        return true;
      } catch (error) {
        throw new Error(error.message);
        return false;
      }
    },

    // Update user profile
    updateUserProfile: async (_, { input }, { req }) => {
      try {
        // Check if user is authenticated
        if (!req.user) {
          throw new AuthenticationError('Not authenticated');
        }

        // Gérer les anciens IDs personnalisés et les nouveaux ObjectIds
        let user;
        try {
          // Essayer d'abord avec l'_id MongoDB
          user = await User.findById(req.user._id);
        } catch (error) {
          // Si ça échoue, essayer avec le champ id personnalisé (pour les anciens utilisateurs)
          user = await User.findOne({ id: req.user.id });
        }

        if (!user) {
          throw new Error('User not found');
        }

        // Update user fields
        if (input.email) user.email = input.email;
        if (input.firstName) user.firstName = input.firstName;
        if (input.lastName) user.lastName = input.lastName;
        if (input.phone) user.phone = input.phone;
        if (input.address) user.address = input.address;
        if (input.photo) user.photo = input.photo;

        // Update password if provided
        if (input.password) {
          user.password = input.password;
        }

        // Save updated user
        const updatedUser = await user.save();

        return updatedUser;
      } catch (error) {
        throw new Error(error.message);
      }
    }
  },

  User: {
    // Resolve drivingSchool field
    drivingSchool: async (parent) => {
      try {
        if (!parent.drivingSchool) return null;
        return await DrivingSchool.findById(parent.drivingSchool);
      } catch (error) {
        throw new Error(error.message);
      }
    },

    // Resolve learner field
    learner: async (parent) => {
      try {
        return await Learner.findOne({ user: parent._id });
      } catch (error) {
        throw new Error(error.message);
      }
    },

    // Resolve employee field
    employee: async (parent) => {
      try {
        return await Employee.findOne({ user: parent._id });
      } catch (error) {
        throw new Error(error.message);
      }
    },

    // Resolve notifications field
    notifications: async (parent) => {
      try {
        return await Notification.find({ user: parent._id });
      } catch (error) {
        throw new Error(error.message);
      }
    },

    // Resolve sentMessages field
    sentMessages: async (parent) => {
      try {
        return await Message.find({ sender: parent._id });
      } catch (error) {
        throw new Error(error.message);
      }
    },

    // Resolve receivedMessages field
    receivedMessages: async (parent) => {
      try {
        return await Message.find({ recipient: parent._id });
      } catch (error) {
        throw new Error(error.message);
      }
    }
  }
};

module.exports = userResolvers;

import React, { useState } from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import { Text, Surface, Button, TextInput, Avatar, Divider, ActivityIndicator } from 'react-native-paper';
import { useQuery, useMutation } from '@apollo/client';
import { GET_CURRENT_USER } from '../../services/graphql/queries';
import { UPDATE_USER_PROFILE } from '../../services/graphql/mutations';
import { useAuth } from '../../contexts/AuthContext';
import { Formik } from 'formik';
import * as Yup from 'yup';
import { router } from 'expo-router';
import StatusTracker from '../../components/learner/StatusTracker';
import DocumentViewer from '../../components/learner/DocumentViewer';

// Validation schema
const ProfileSchema = Yup.object().shape({
  firstName: Yup.string().required('Prénom requis'),
  lastName: Yup.string().required('Nom requis'),
  email: Yup.string().email('Email invalide').required('Email requis'),
  phone: Yup.string().matches(/^[0-9]{10}$/, 'Numéro de téléphone invalide'),
});

export default function ProfileScreen() {
  const { user } = useAuth();
  const [isEditing, setIsEditing] = useState(false);

  // Get current user data
  const { loading, error, data, refetch } = useQuery(GET_CURRENT_USER);

  // Update profile mutation
  const [updateProfile, { loading: updateLoading, error: updateError }] = useMutation(UPDATE_USER_PROFILE);

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" />
        <Text style={styles.loadingText}>Chargement...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>Erreur de chargement des données utilisateur</Text>
        <Text>{error.message}</Text>
      </View>
    );
  }

  // Données de test temporaires pour les documents
  const testDocuments = {
    cin: {
      file: 'https://via.placeholder.com/400x300/2196F3/white?text=CIN+Recto',
      status: 'ACCEPTE',
      comment: 'Document validé par l\'administration',
      uploadedAt: '2024-01-16T10:30:00Z',
      validatedAt: '2024-01-17T14:20:00Z'
    },
    medicalCertificate: {
      file: 'https://via.placeholder.com/400x300/4CAF50/white?text=Certificat+Medical',
      status: 'ACCEPTE',
      comment: 'Certificat médical valide',
      uploadedAt: '2024-01-16T11:15:00Z',
      validatedAt: '2024-01-17T15:10:00Z'
    },
    photo: {
      file: 'https://via.placeholder.com/300x400/FF9800/white?text=Photo+Identite',
      status: 'EN_ATTENTE',
      comment: 'En cours de validation',
      uploadedAt: '2024-01-16T12:00:00Z',
      validatedAt: null
    }
  };

  const userData = data?.me || user;

  // Ajouter les documents de test si l'utilisateur est un apprenant et n'a pas de documents
  if (userData?.role === 'APPRENANT' && userData?.learner && !userData.learner.documents) {
    userData.learner.documents = testDocuments;
  }

  const handleUpdateProfile = async (values) => {
    try {
      await updateProfile({
        variables: {
          input: {
            firstName: values.firstName,
            lastName: values.lastName,
            phone: values.phone,
            address: values.address,
          }
        }
      });

      // Refetch user data
      await refetch();

      // Exit edit mode
      setIsEditing(false);
    } catch (err) {
      console.error('Error updating profile:', err);
    }
  };

  return (
    <ScrollView style={styles.container}>
      <Surface style={styles.header}>
        <View style={styles.avatarContainer}>
          {userData.photo ? (
            <Avatar.Image
              size={100}
              source={{ uri: userData.photo }}
            />
          ) : (
            <Avatar.Text
              size={100}
              label={`${userData.firstName.charAt(0)}${userData.lastName.charAt(0)}`}
            />
          )}

          {/* Bouton pour changer la photo */}
          <Button
            mode="outlined"
            onPress={() => router.push('/profile-photo')}
            icon="camera"
            style={styles.photoButton}
            compact
          >
            Changer
          </Button>
        </View>

        <Text style={styles.name}>
          {userData.firstName} {userData.lastName}
        </Text>
       <Text style={styles.role}>
        {userData.role === 'APPRENANT' ? 'Apprenant' :
        userData.role === 'MONITEUR' ? 'Moniteur' :
        userData.role === 'ADMINISTRATEUR' ? 'Administrateur' :
        userData.role === 'INSTRUCTEUR' ? 'Instructeur' :
        userData.role === 'SECRETAIRE' ? 'Secrétaire' :
        userData.role === 'MANAGER' ? 'Manager' : 'Utilisateur'}
      </Text>
      </Surface>

      {updateError && (
        <Text style={styles.errorText}>
          Erreur lors de la mise à jour du profil: {updateError.message}
        </Text>
      )}

      {/* Afficher le suivi de statut seulement pour les apprenants */}
      {userData.role === 'APPRENANT' && (
        <StatusTracker learner={userData.learner} />
      )}

      {/* Afficher les documents seulement pour les apprenants */}
      {userData.role === 'APPRENANT' && (
        <DocumentViewer
          documents={userData.learner?.documents || {}}
          userRole={userData.role}
        />
      )}

      <Surface style={styles.infoCard}>
        {isEditing ? (
          <Formik
            initialValues={{
              firstName: userData.firstName || '',
              lastName: userData.lastName || '',
              email: userData.email || '',
              phone: userData.phone || '',
              address: userData.address || '',
            }}
            validationSchema={ProfileSchema}
            onSubmit={handleUpdateProfile}
          >
            {({ handleChange, handleBlur, handleSubmit, values, errors, touched }) => (
              <View>
                <TextInput
                  label="Prénom"
                  value={values.firstName}
                  onChangeText={handleChange('firstName')}
                  onBlur={handleBlur('firstName')}
                  error={touched.firstName && errors.firstName}
                  style={styles.input}
                  mode="outlined"
                />
                {touched.firstName && errors.firstName && (
                  <Text style={styles.errorText}>{errors.firstName}</Text>
                )}

                <TextInput
                  label="Nom"
                  value={values.lastName}
                  onChangeText={handleChange('lastName')}
                  onBlur={handleBlur('lastName')}
                  error={touched.lastName && errors.lastName}
                  style={styles.input}
                  mode="outlined"
                />
                {touched.lastName && errors.lastName && (
                  <Text style={styles.errorText}>{errors.lastName}</Text>
                )}

                <TextInput
                  label="Email"
                  value={values.email}
                  onChangeText={handleChange('email')}
                  onBlur={handleBlur('email')}
                  error={touched.email && errors.email}
                  style={styles.input}
                  mode="outlined"
                  disabled={true} // Email cannot be changed
                />

                <TextInput
                  label="Téléphone"
                  value={values.phone}
                  onChangeText={handleChange('phone')}
                  onBlur={handleBlur('phone')}
                  error={touched.phone && errors.phone}
                  style={styles.input}
                  mode="outlined"
                  keyboardType="phone-pad"
                />
                {touched.phone && errors.phone && (
                  <Text style={styles.errorText}>{errors.phone}</Text>
                )}

                <TextInput
                  label="Adresse"
                  value={values.address}
                  onChangeText={handleChange('address')}
                  onBlur={handleBlur('address')}
                  style={styles.input}
                  mode="outlined"
                />

                <View style={styles.buttonContainer}>
                  <Button
                    mode="contained"
                    onPress={handleSubmit}
                    style={styles.button}
                    loading={updateLoading}
                    disabled={updateLoading}
                  >
                    Enregistrer
                  </Button>
                  <Button
                    mode="outlined"
                    onPress={() => setIsEditing(false)}
                    style={styles.button}
                    disabled={updateLoading}
                  >
                    Annuler
                  </Button>
                </View>
              </View>
            )}
          </Formik>
        ) : (
          <View>
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Email:</Text>
              <Text style={styles.infoValue}>{userData.email}</Text>
            </View>

            <Divider style={styles.divider} />

            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Téléphone:</Text>
              <Text style={styles.infoValue}>{userData.phone || 'Non renseigné'}</Text>
            </View>

            <Divider style={styles.divider} />

            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Adresse:</Text>
              <Text style={styles.infoValue}>{userData.address || 'Non renseignée'}</Text>
            </View>

            <Divider style={styles.divider} />

            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Date d'inscription:</Text>
              <Text style={styles.infoValue}>
                {userData.createdAt ? new Date(userData.createdAt).toLocaleDateString() : 'Non disponible'}
              </Text>
            </View>

            <Button
              mode="contained"
              onPress={() => setIsEditing(true)}
              style={styles.editButton}
            >
              Modifier le profil
            </Button>
          </View>
        )}
      </Surface>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    color: 'red',
    marginBottom: 10,
  },
  header: {
    padding: 20,
    alignItems: 'center',
    borderRadius: 10,
    marginBottom: 20,
    elevation: 4,
  },
  avatarContainer: {
    marginBottom: 10,
    alignItems: 'center',
  },
  photoButton: {
    marginTop: 8,
    borderColor: '#6200ee',
  },
  name: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  role: {
    fontSize: 16,
    color: '#666',
  },
  infoCard: {
    padding: 20,
    borderRadius: 10,
    elevation: 4,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 10,
  },
  infoLabel: {
    fontWeight: 'bold',
    color: '#666',
  },
  infoValue: {
    flex: 1,
    textAlign: 'right',
  },
  divider: {
    marginVertical: 5,
  },
  editButton: {
    marginTop: 20,
  },
  input: {
    marginBottom: 10,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 10,
  },
  button: {
    flex: 1,
    marginHorizontal: 5,
  },
});

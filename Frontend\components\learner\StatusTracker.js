import React from 'react';
import { View, StyleSheet } from 'react-native';
import {
  Card,
  Title,
  Text,
  ProgressBar,
  Chip,
  Surface,
  Divider
} from 'react-native-paper';
import { MaterialIcons } from '@expo/vector-icons';

const StatusTracker = ({ learner }) => {
  // Données de test si aucun apprenant n'est fourni
  const testLearner = learner || {
    status: 'EN_COURS', // Ce sera recalculé automatiquement
    progress: 45,
    progressDetails: {
      theory: {
        percentage: 75,
        lessonsCompleted: 15,
        totalLessons: 20
      },
      driving: {
        percentage: 35,
        hoursCompleted: 7, // 7 heures sur 20 = statut "EN_COURS"
        totalHours: 20
      }
    },
    fees: {
      total: 1200,
      paid: 800,
      remaining: 400
    },
    documents: {
      cin: { status: 'ACCEPTE' },
      medicalCertificate: { status: 'ACCEPTE' },
      photo: { status: 'EN_ATTENTE' }
    }
  };
  // Configuration des statuts
  const statusConfig = {
    INSCRIT: {
      label: 'Inscrit',
      description: 'Inscription enregistrée - Formation pas encore commencée',
      color: '#2196F3',
      backgroundColor: '#E3F2FD',
      icon: 'person-add',
      step: 1,
      progress: 0
    },
    EN_COURS: {
      label: 'En cours',
      description: 'Formation en cours - Heures de conduite en progression',
      color: '#FF9800',
      backgroundColor: '#FFF3E0',
      icon: 'school',
      step: 2,
      progress: 0.5
    },
    TERMINE: {
      label: 'Terminé',
      description: 'Formation terminée - Toutes les heures effectuées',
      color: '#4CAF50',
      backgroundColor: '#E8F5E8',
      icon: 'check-circle',
      step: 3,
      progress: 1.0
    },
    SUSPENDU: {
      label: 'Suspendu',
      description: 'Formation suspendue par l\'auto-école',
      color: '#F44336',
      backgroundColor: '#FFEBEE',
      icon: 'pause-circle-filled',
      step: 0,
      progress: 0
    }
  };

  // Calculer le statut automatique basé sur les heures (sauf si suspendu)
  const calculateAutoStatus = () => {
    if (testLearner?.status === 'SUSPENDU') return 'SUSPENDU';

    const hoursCompleted = testLearner?.progressDetails?.driving?.hoursCompleted || 0;
    const totalHours = testLearner?.progressDetails?.driving?.totalHours || 20;

    if (hoursCompleted === 0) return 'INSCRIT';
    if (hoursCompleted >= totalHours) return 'TERMINE';
    return 'EN_COURS';
  };

  const autoStatus = calculateAutoStatus();
  const currentStatus = statusConfig[autoStatus];
  const allStatuses = ['INSCRIT', 'EN_COURS', 'TERMINE'];

  // Calculer la progression basée sur les heures de conduite
  const calculateOverallProgress = () => {
    if (autoStatus === 'SUSPENDU') return 0;
    if (autoStatus === 'TERMINE') return 100;

    const hoursCompleted = testLearner?.progressDetails?.driving?.hoursCompleted || 0;
    const totalHours = testLearner?.progressDetails?.driving?.totalHours || 20;

    // Progression basée sur les heures de conduite (70%) + théorie (30%)
    const drivingProgress = (hoursCompleted / totalHours) * 70;
    const theoryProgress = ((testLearner?.progressDetails?.theory?.percentage || 0) / 100) * 30;

    return Math.min(drivingProgress + theoryProgress, 100);
  };

  const overallProgress = calculateOverallProgress();

  // Obtenir la couleur de progression
  const getProgressColor = () => {
    if (autoStatus === 'SUSPENDU') return '#F44336';
    if (autoStatus === 'INSCRIT') return '#2196F3';
    if (autoStatus === 'EN_COURS') return '#FF9800';
    if (autoStatus === 'TERMINE') return '#4CAF50';
    return '#2196F3';
  };

  return (
    <Card style={styles.container}>
      <Card.Content>
        <Title style={styles.title}>Suivi de votre formation</Title>

        {/* Statut actuel */}
        <Surface style={[styles.currentStatus, { backgroundColor: currentStatus.backgroundColor }]}>
          <View style={styles.statusHeader}>
            <MaterialIcons
              name={currentStatus.icon}
              size={32}
              color={currentStatus.color}
            />
            <View style={styles.statusInfo}>
              <Text style={[styles.statusLabel, { color: currentStatus.color }]}>
                {currentStatus.label}
              </Text>
              <Text style={styles.statusDescription}>
                {currentStatus.description}
              </Text>
            </View>
            <Chip
              mode="flat"
              style={[styles.statusChip, { backgroundColor: currentStatus.color }]}
              textStyle={styles.chipText}
            >
              {currentStatus.label}
            </Chip>
          </View>
        </Surface>

        <Divider style={styles.divider} />

        {/* Progression globale simplifiée */}
        <View style={styles.progressSection}>
          <View style={styles.progressHeader}>
            <MaterialIcons name="trending-up" size={20} color={getProgressColor()} />
            <Text style={styles.progressTitle}>Progression globale de la formation</Text>
            <Text style={[styles.progressPercentage, { color: getProgressColor() }]}>
              {Math.round(overallProgress)}%
            </Text>
          </View>

          <ProgressBar
            progress={overallProgress / 100}
            color={getProgressColor()}
            style={styles.progressBar}
          />

          <Text style={styles.progressSubtext}>
            Combinaison de votre progression en conduite (70%) et en théorie (30%)
          </Text>

          {/* Message contextuel plus détaillé */}
          <View style={styles.statusMessage}>
            {autoStatus === 'SUSPENDU' ? (
              <View style={styles.messageContainer}>
                <Text style={[styles.messageTitle, { color: '#F44336' }]}>⚠️ Formation suspendue</Text>
                <Text style={styles.messageText}>
                  Votre formation a été suspendue par l'auto-école. Contactez l'administration pour plus d'informations.
                </Text>
              </View>
            ) : autoStatus === 'TERMINE' ? (
              <View style={styles.messageContainer}>
                <Text style={[styles.messageTitle, { color: '#4CAF50' }]}>🎉 Formation terminée</Text>
                <Text style={styles.messageText}>
                  Félicitations ! Vous avez terminé toutes vos {testLearner?.progressDetails?.driving?.totalHours || 20} heures de conduite.
                  Vous pouvez maintenant passer votre examen final.
                </Text>
              </View>
            ) : autoStatus === 'INSCRIT' ? (
              <View style={styles.messageContainer}>
                <Text style={[styles.messageTitle, { color: '#2196F3' }]}>📝 Prêt à commencer</Text>
                <Text style={styles.messageText}>
                  Votre inscription est validée. Contactez votre auto-école pour planifier votre première séance de conduite.
                </Text>
              </View>
            ) : (
              <View style={styles.messageContainer}>
                <Text style={[styles.messageTitle, { color: '#FF9800' }]}>🚗 Formation en cours</Text>
                <Text style={styles.messageText}>
                  Vous avez effectué {testLearner?.progressDetails?.driving?.hoursCompleted || 0} heures sur {testLearner?.progressDetails?.driving?.totalHours || 20} requises.
                  Il vous reste {(testLearner?.progressDetails?.driving?.totalHours || 20) - (testLearner?.progressDetails?.driving?.hoursCompleted || 0)} heures à compléter.
                </Text>
              </View>
            )}
          </View>
        </View>

        <Divider style={styles.divider} />

        {/* Informations détaillées sur les heures */}
        <View style={styles.hoursSection}>
          <View style={styles.hoursSectionHeader}>
            <MaterialIcons name="drive-eta" size={24} color={getProgressColor()} />
            <Text style={styles.hoursTitle}>Suivi des heures de conduite</Text>
          </View>

          {/* Barre de progression des heures */}
          <View style={styles.hoursProgressContainer}>
            <View style={styles.hoursProgressHeader}>
              <Text style={styles.hoursProgressText}>
                {testLearner?.progressDetails?.driving?.hoursCompleted || 0} / {testLearner?.progressDetails?.driving?.totalHours || 20} heures
              </Text>
              <Text style={[styles.hoursPercentage, { color: getProgressColor() }]}>
                {Math.round(((testLearner?.progressDetails?.driving?.hoursCompleted || 0) / (testLearner?.progressDetails?.driving?.totalHours || 20)) * 100)}%
              </Text>
            </View>
            <ProgressBar
              progress={(testLearner?.progressDetails?.driving?.hoursCompleted || 0) / (testLearner?.progressDetails?.driving?.totalHours || 20)}
              color={getProgressColor()}
              style={styles.hoursProgressBar}
            />
          </View>

          {/* Détails des heures */}
          <View style={styles.hoursInfo}>
            <View style={[styles.hoursItem, styles.hoursItemCompleted]}>
              <MaterialIcons name="check-circle" size={20} color="#4CAF50" />
              <View style={styles.hoursItemContent}>
                <Text style={styles.hoursLabel}>Heures effectuées</Text>
                <Text style={[styles.hoursValue, { color: '#4CAF50' }]}>
                  {testLearner?.progressDetails?.driving?.hoursCompleted || 0}h
                </Text>
              </View>
            </View>

            <View style={[styles.hoursItem, styles.hoursItemRemaining]}>
              <MaterialIcons name="schedule" size={20} color="#FF9800" />
              <View style={styles.hoursItemContent}>
                <Text style={styles.hoursLabel}>Heures restantes</Text>
                <Text style={[styles.hoursValue, { color: '#FF9800' }]}>
                  {(testLearner?.progressDetails?.driving?.totalHours || 20) - (testLearner?.progressDetails?.driving?.hoursCompleted || 0)}h
                </Text>
              </View>
            </View>
          </View>

          {/* Prochaine étape */}
          {autoStatus === 'EN_COURS' && (
            <View style={styles.nextStepContainer}>
              <Text style={styles.nextStepTitle}>🎯 Prochaine étape</Text>
              <Text style={styles.nextStepText}>
                Planifiez vos prochaines séances de conduite pour progresser vers les {testLearner?.progressDetails?.driving?.totalHours || 20} heures requises.
              </Text>
            </View>
          )}
        </View>

        <Divider style={styles.divider} />

        {/* Échelle des statuts */}
        <View style={styles.statusScale}>
          <Text style={styles.scaleTitle}>Étapes de formation</Text>

          <View style={styles.stepsContainer}>
            {allStatuses.map((status, index) => {
              const config = statusConfig[status];
              const isActive = autoStatus === status;
              const isCompleted = currentStatus.step > config.step || autoStatus === 'TERMINE';
              const isPending = currentStatus.step < config.step && autoStatus !== 'SUSPENDU';

              return (
                <View key={status} style={styles.stepContainer}>
                  <View style={styles.stepLine}>
                    {index > 0 && (
                      <View style={[
                        styles.connector,
                        { backgroundColor: isCompleted ? config.color : '#E0E0E0' }
                      ]} />
                    )}

                    <View style={[
                      styles.stepCircle,
                      {
                        backgroundColor: isActive ? config.color :
                                       isCompleted ? config.color :
                                       isPending ? '#E0E0E0' : '#F5F5F5',
                        borderColor: config.color,
                        borderWidth: isActive ? 3 : 1
                      }
                    ]}>
                      <MaterialIcons
                        name={isCompleted ? 'check' : config.icon}
                        size={16}
                        color={isActive || isCompleted ? 'white' : '#999'}
                      />
                    </View>
                  </View>

                  <Text style={[
                    styles.stepLabel,
                    {
                      color: isActive ? config.color :
                             isCompleted ? config.color : '#999',
                      fontWeight: isActive ? 'bold' : 'normal'
                    }
                  ]}>
                    {config.label}
                  </Text>
                </View>
              );
            })}
          </View>
        </View>

        {/* Note explicative améliorée */}
        <View style={styles.noteSection}>
          <View style={styles.noteHeader}>
            <MaterialIcons name="info" size={20} color="#2196F3" />
            <Text style={styles.noteTitle}>Comment fonctionne votre statut ?</Text>
          </View>
          <Text style={styles.noteText}>
            Votre statut de formation est automatiquement calculé en fonction de vos heures de conduite :
          </Text>
          <View style={styles.noteList}>
            <Text style={styles.noteListItem}>• <Text style={styles.noteListLabel}>Inscrit</Text> : 0 heure effectuée</Text>
            <Text style={styles.noteListItem}>• <Text style={styles.noteListLabel}>En cours</Text> : Formation commencée</Text>
            <Text style={styles.noteListItem}>• <Text style={styles.noteListLabel}>Terminé</Text> : {testLearner?.progressDetails?.driving?.totalHours || 20} heures complétées</Text>
            <Text style={styles.noteListItem}>• <Text style={styles.noteListLabel}>Suspendu</Text> : Géré par l'auto-école</Text>
          </View>
        </View>

        {/* Détails de progression */}
        {testLearner?.progressDetails && (
          <>
            <Divider style={styles.divider} />
            <View style={styles.detailsSection}>
              <Text style={styles.detailsTitle}>Détails de progression</Text>

              <View style={styles.detailItem}>
                <View style={styles.detailHeader}>
                  <MaterialIcons name="menu-book" size={20} color="#2196F3" />
                  <Text style={styles.detailLabel}>Théorie</Text>
                  <Text style={styles.detailPercentage}>
                    {Math.round(testLearner.progressDetails.theory?.percentage || 0)}%
                  </Text>
                </View>
                <ProgressBar
                  progress={(testLearner.progressDetails.theory?.percentage || 0) / 100}
                  color="#2196F3"
                  style={styles.detailProgressBar}
                />
                <Text style={styles.detailDescription}>
                  {testLearner.progressDetails.theory?.lessonsCompleted || 0} / {testLearner.progressDetails.theory?.totalLessons || 0} cours
                </Text>
              </View>

              <View style={styles.detailItem}>
                <View style={styles.detailHeader}>
                  <MaterialIcons name="drive-eta" size={20} color="#FF9800" />
                  <Text style={styles.detailLabel}>Conduite</Text>
                  <Text style={styles.detailPercentage}>
                    {Math.round(testLearner.progressDetails.driving?.percentage || 0)}%
                  </Text>
                </View>
                <ProgressBar
                  progress={(testLearner.progressDetails.driving?.percentage || 0) / 100}
                  color="#FF9800"
                  style={styles.detailProgressBar}
                />
                <Text style={styles.detailDescription}>
                  {testLearner.progressDetails.driving?.hoursCompleted || 0} / {testLearner.progressDetails.driving?.totalHours || 20} heures
                </Text>
              </View>
            </View>
          </>
        )}
      </Card.Content>
    </Card>
  );
};

const styles = StyleSheet.create({
  container: {
    margin: 16,
    elevation: 4,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
    textAlign: 'center',
    color: '#333',
  },
  currentStatus: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
  },
  statusHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusInfo: {
    flex: 1,
    marginLeft: 12,
  },
  statusLabel: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  statusDescription: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  statusChip: {
    marginLeft: 8,
  },
  chipText: {
    color: 'white',
    fontWeight: 'bold',
  },
  divider: {
    marginVertical: 16,
  },
  hoursSection: {
    marginBottom: 16,
    backgroundColor: 'white',
    padding: 16,
    borderRadius: 12,
    elevation: 2,
  },
  hoursSectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  hoursTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginLeft: 8,
    flex: 1,
  },
  hoursProgressContainer: {
    marginBottom: 16,
  },
  hoursProgressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  hoursProgressText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
  },
  hoursPercentage: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  hoursProgressBar: {
    height: 10,
    borderRadius: 5,
  },
  hoursInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  hoursItem: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    marginHorizontal: 4,
  },
  hoursItemCompleted: {
    backgroundColor: '#E8F5E8',
  },
  hoursItemRemaining: {
    backgroundColor: '#FFF3E0',
  },
  hoursItemContent: {
    marginLeft: 8,
    flex: 1,
  },
  hoursLabel: {
    fontSize: 12,
    color: '#666',
    marginBottom: 2,
  },
  hoursValue: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  nextStepContainer: {
    backgroundColor: '#e8f4fd',
    padding: 12,
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: '#FF9800',
  },
  nextStepTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#FF9800',
    marginBottom: 4,
  },
  nextStepText: {
    fontSize: 13,
    color: '#666',
    lineHeight: 18,
  },
  progressSection: {
    marginBottom: 16,
  },
  progressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  progressTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  progressPercentage: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  progressBar: {
    height: 8,
    borderRadius: 4,
    marginBottom: 8,
  },
  progressSubtext: {
    fontSize: 12,
    color: '#999',
    textAlign: 'center',
    fontStyle: 'italic',
  },
  statusMessage: {
    marginTop: 12,
  },
  messageContainer: {
    backgroundColor: '#f8f9fa',
    padding: 16,
    borderRadius: 12,
    borderLeftWidth: 4,
    borderLeftColor: '#2196F3',
  },
  messageTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  messageText: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  statusScale: {
    marginBottom: 16,
  },
  scaleTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
    textAlign: 'center',
  },
  stepsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  stepContainer: {
    flex: 1,
    alignItems: 'center',
  },
  stepLine: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '100%',
    justifyContent: 'center',
    position: 'relative',
  },
  connector: {
    position: 'absolute',
    left: 0,
    right: '50%',
    height: 2,
    top: '50%',
    marginTop: -1,
  },
  stepCircle: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1,
  },
  stepLabel: {
    fontSize: 12,
    marginTop: 8,
    textAlign: 'center',
  },
  detailsSection: {
    marginTop: 8,
  },
  detailsTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 12,
  },
  detailItem: {
    marginBottom: 16,
  },
  detailHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  detailLabel: {
    flex: 1,
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 8,
    color: '#333',
  },
  detailPercentage: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
  },
  detailProgressBar: {
    height: 6,
    borderRadius: 3,
    marginBottom: 4,
  },
  detailDescription: {
    fontSize: 12,
    color: '#666',
    textAlign: 'right',
  },
  noteSection: {
    backgroundColor: '#e8f4fd',
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
    borderLeftWidth: 4,
    borderLeftColor: '#2196F3',
  },
  noteHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  noteTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#1976D2',
    marginLeft: 8,
  },
  noteText: {
    fontSize: 13,
    color: '#1976D2',
    marginBottom: 8,
    lineHeight: 18,
  },
  noteList: {
    marginLeft: 8,
  },
  noteListItem: {
    fontSize: 12,
    color: '#1976D2',
    marginBottom: 4,
    lineHeight: 16,
  },
  noteListLabel: {
    fontWeight: 'bold',
  },
});

export default StatusTracker;

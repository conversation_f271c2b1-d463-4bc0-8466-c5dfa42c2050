import React from 'react';
import { View, StyleSheet } from 'react-native';
import {
  Card,
  Title,
  Text,
  ProgressBar,
  Chip,
  Surface,
  Divider
} from 'react-native-paper';
import { MaterialIcons } from '@expo/vector-icons';

const StatusTracker = ({ learner }) => {
  // Données de test si aucun apprenant n'est fourni
  const testLearner = learner || {
    status: 'EN_COURS',
    progress: 45,
    progressDetails: {
      theory: {
        percentage: 60,
        lessonsCompleted: 12,
        totalLessons: 20
      },
      driving: {
        percentage: 35,
        hoursCompleted: 7,
        totalHours: 20
      }
    },
    fees: {
      total: 1200,
      paid: 800,
      remaining: 400
    },
    documents: {
      cin: { status: 'ACCEPTE' },
      medicalCertificate: { status: 'ACCEPTE' },
      photo: { status: 'EN_ATTENTE' }
    }
  };
  // Configuration des statuts
  const statusConfig = {
    INSCRIT: {
      label: 'Inscrit',
      description: 'Votre inscription a été enregistrée',
      color: '#2196F3',
      backgroundColor: '#E3F2FD',
      icon: 'person-add',
      step: 1,
      progress: 0.25
    },
    EN_COURS: {
      label: 'En cours',
      description: 'Formation en cours de réalisation',
      color: '#FF9800',
      backgroundColor: '#FFF3E0',
      icon: 'school',
      step: 2,
      progress: 0.5
    },
    TERMINE: {
      label: 'Terminé',
      description: 'Formation terminée avec succès',
      color: '#4CAF50',
      backgroundColor: '#E8F5E8',
      icon: 'check-circle',
      step: 3,
      progress: 1.0
    },
    SUSPENDU: {
      label: 'Suspendu',
      description: 'Formation temporairement suspendue',
      color: '#F44336',
      backgroundColor: '#FFEBEE',
      icon: 'pause-circle-filled',
      step: 0,
      progress: 0
    }
  };

  const currentStatus = statusConfig[testLearner?.status || 'INSCRIT'];
  const allStatuses = ['INSCRIT', 'EN_COURS', 'TERMINE'];

  // Calculer la progression basée sur le statut et les détails
  const calculateOverallProgress = () => {
    if (testLearner?.status === 'SUSPENDU') return 0;
    if (testLearner?.status === 'TERMINE') return 100;

    // Pour les autres statuts, utiliser la progression détaillée
    const baseProgress = currentStatus.progress * 100;
    const detailProgress = testLearner?.progress || 0;

    // Combiner la progression du statut avec les détails
    return Math.min(baseProgress + (detailProgress * 0.3), 100);
  };

  const overallProgress = calculateOverallProgress();

  // Obtenir la couleur de progression
  const getProgressColor = () => {
    if (testLearner?.status === 'SUSPENDU') return '#F44336';
    if (overallProgress < 30) return '#2196F3';
    if (overallProgress < 70) return '#FF9800';
    return '#4CAF50';
  };

  return (
    <Card style={styles.container}>
      <Card.Content>
        <Title style={styles.title}>Suivi de votre formation</Title>

        {/* Statut actuel */}
        <Surface style={[styles.currentStatus, { backgroundColor: currentStatus.backgroundColor }]}>
          <View style={styles.statusHeader}>
            <MaterialIcons
              name={currentStatus.icon}
              size={32}
              color={currentStatus.color}
            />
            <View style={styles.statusInfo}>
              <Text style={[styles.statusLabel, { color: currentStatus.color }]}>
                {currentStatus.label}
              </Text>
              <Text style={styles.statusDescription}>
                {currentStatus.description}
              </Text>
            </View>
            <Chip
              mode="flat"
              style={[styles.statusChip, { backgroundColor: currentStatus.color }]}
              textStyle={styles.chipText}
            >
              {currentStatus.label}
            </Chip>
          </View>
        </Surface>

        <Divider style={styles.divider} />

        {/* Progression globale */}
        <View style={styles.progressSection}>
          <View style={styles.progressHeader}>
            <Text style={styles.progressTitle}>Progression globale</Text>
            <Text style={[styles.progressPercentage, { color: getProgressColor() }]}>
              {Math.round(overallProgress)}%
            </Text>
          </View>

          <ProgressBar
            progress={overallProgress / 100}
            color={getProgressColor()}
            style={styles.progressBar}
          />

          <Text style={styles.progressDescription}>
            {testLearner?.status === 'SUSPENDU'
              ? 'Formation suspendue - Contactez votre auto-école'
              : testLearner?.status === 'TERMINE'
              ? 'Félicitations ! Votre formation est terminée'
              : `Continuez votre formation pour atteindre ${currentStatus.label === 'Inscrit' ? 'le niveau En cours' : 'la fin'}`
            }
          </Text>
        </View>

        <Divider style={styles.divider} />

        {/* Échelle des statuts */}
        <View style={styles.statusScale}>
          <Text style={styles.scaleTitle}>Étapes de formation</Text>

          <View style={styles.stepsContainer}>
            {allStatuses.map((status, index) => {
              const config = statusConfig[status];
              const isActive = testLearner?.status === status;
              const isCompleted = currentStatus.step > config.step || testLearner?.status === 'TERMINE';
              const isPending = currentStatus.step < config.step && testLearner?.status !== 'SUSPENDU';

              return (
                <View key={status} style={styles.stepContainer}>
                  <View style={styles.stepLine}>
                    {index > 0 && (
                      <View style={[
                        styles.connector,
                        { backgroundColor: isCompleted ? config.color : '#E0E0E0' }
                      ]} />
                    )}

                    <View style={[
                      styles.stepCircle,
                      {
                        backgroundColor: isActive ? config.color :
                                       isCompleted ? config.color :
                                       isPending ? '#E0E0E0' : '#F5F5F5',
                        borderColor: config.color,
                        borderWidth: isActive ? 3 : 1
                      }
                    ]}>
                      <MaterialIcons
                        name={isCompleted ? 'check' : config.icon}
                        size={16}
                        color={isActive || isCompleted ? 'white' : '#999'}
                      />
                    </View>
                  </View>

                  <Text style={[
                    styles.stepLabel,
                    {
                      color: isActive ? config.color :
                             isCompleted ? config.color : '#999',
                      fontWeight: isActive ? 'bold' : 'normal'
                    }
                  ]}>
                    {config.label}
                  </Text>
                </View>
              );
            })}
          </View>
        </View>

        {/* Détails de progression */}
        {testLearner?.progressDetails && (
          <>
            <Divider style={styles.divider} />
            <View style={styles.detailsSection}>
              <Text style={styles.detailsTitle}>Détails de progression</Text>

              <View style={styles.detailItem}>
                <View style={styles.detailHeader}>
                  <MaterialIcons name="menu-book" size={20} color="#2196F3" />
                  <Text style={styles.detailLabel}>Théorie</Text>
                  <Text style={styles.detailPercentage}>
                    {Math.round(testLearner.progressDetails.theory?.percentage || 0)}%
                  </Text>
                </View>
                <ProgressBar
                  progress={(testLearner.progressDetails.theory?.percentage || 0) / 100}
                  color="#2196F3"
                  style={styles.detailProgressBar}
                />
                <Text style={styles.detailDescription}>
                  {testLearner.progressDetails.theory?.lessonsCompleted || 0} / {testLearner.progressDetails.theory?.totalLessons || 0} cours
                </Text>
              </View>

              <View style={styles.detailItem}>
                <View style={styles.detailHeader}>
                  <MaterialIcons name="drive-eta" size={20} color="#FF9800" />
                  <Text style={styles.detailLabel}>Conduite</Text>
                  <Text style={styles.detailPercentage}>
                    {Math.round(testLearner.progressDetails.driving?.percentage || 0)}%
                  </Text>
                </View>
                <ProgressBar
                  progress={(testLearner.progressDetails.driving?.percentage || 0) / 100}
                  color="#FF9800"
                  style={styles.detailProgressBar}
                />
                <Text style={styles.detailDescription}>
                  {testLearner.progressDetails.driving?.hoursCompleted || 0} / {testLearner.progressDetails.driving?.totalHours || 20} heures
                </Text>
              </View>
            </View>
          </>
        )}
      </Card.Content>
    </Card>
  );
};

const styles = StyleSheet.create({
  container: {
    margin: 16,
    elevation: 4,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
    textAlign: 'center',
    color: '#333',
  },
  currentStatus: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
  },
  statusHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusInfo: {
    flex: 1,
    marginLeft: 12,
  },
  statusLabel: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  statusDescription: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  statusChip: {
    marginLeft: 8,
  },
  chipText: {
    color: 'white',
    fontWeight: 'bold',
  },
  divider: {
    marginVertical: 16,
  },
  progressSection: {
    marginBottom: 16,
  },
  progressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  progressTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  progressPercentage: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  progressBar: {
    height: 8,
    borderRadius: 4,
    marginBottom: 8,
  },
  progressDescription: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
  },
  statusScale: {
    marginBottom: 16,
  },
  scaleTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
    textAlign: 'center',
  },
  stepsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  stepContainer: {
    flex: 1,
    alignItems: 'center',
  },
  stepLine: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '100%',
    justifyContent: 'center',
    position: 'relative',
  },
  connector: {
    position: 'absolute',
    left: 0,
    right: '50%',
    height: 2,
    top: '50%',
    marginTop: -1,
  },
  stepCircle: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1,
  },
  stepLabel: {
    fontSize: 12,
    marginTop: 8,
    textAlign: 'center',
  },
  detailsSection: {
    marginTop: 8,
  },
  detailsTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 12,
  },
  detailItem: {
    marginBottom: 16,
  },
  detailHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  detailLabel: {
    flex: 1,
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 8,
    color: '#333',
  },
  detailPercentage: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
  },
  detailProgressBar: {
    height: 6,
    borderRadius: 3,
    marginBottom: 4,
  },
  detailDescription: {
    fontSize: 12,
    color: '#666',
    textAlign: 'right',
  },
});

export default StatusTracker;

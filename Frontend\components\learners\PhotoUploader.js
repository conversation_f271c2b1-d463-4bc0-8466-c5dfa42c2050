import React, { useState } from 'react';
import { View, StyleSheet, Alert, Image, Platform } from 'react-native';
import { 
  Text, 
  Card, 
  Title, 
  Button, 
  ActivityIndicator,
  IconButton,
  Chip,
  HelperText
} from 'react-native-paper';
import * as ImagePicker from 'expo-image-picker';
import { useMutation } from '@apollo/client';
import { UPLOAD_DOCUMENT } from '../../services/graphql/mutations';

const PhotoUploader = ({ 
  learnerId, 
  currentPhoto = null, 
  onPhotoUploaded, 
  editable = true,
  showStatus = true 
}) => {
  const [selectedImage, setSelectedImage] = useState(null);
  const [uploading, setUploading] = useState(false);

  const [uploadDocument] = useMutation(UPLOAD_DOCUMENT, {
    onCompleted: (data) => {
      Alert.alert('Succès', 'Photo téléchargée avec succès');
      setSelectedImage(null);
      if (onPhotoUploaded) {
        onPhotoUploaded(data.uploadDocument);
      }
    },
    onError: (error) => {
      Alert.alert('Erreur', error.message || 'Erreur lors du téléchargement');
      setUploading(false);
    }
  });

  // Demander les permissions
  const requestPermissions = async () => {
    if (Platform.OS !== 'web') {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert(
          'Permission requise',
          'Nous avons besoin de la permission d\'accéder à vos photos pour télécharger une image.'
        );
        return false;
      }
    }
    return true;
  };

  // Sélectionner une image depuis la galerie
  const pickImageFromGallery = async () => {
    const hasPermission = await requestPermissions();
    if (!hasPermission) return;

    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1], // Format carré pour photo d'identité
        quality: 0.8,
        base64: true,
      });

      if (!result.canceled && result.assets[0]) {
        setSelectedImage(result.assets[0]);
      }
    } catch (error) {
      Alert.alert('Erreur', 'Erreur lors de la sélection de l\'image');
    }
  };

  // Prendre une photo avec la caméra
  const takePhoto = async () => {
    if (Platform.OS !== 'web') {
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert(
          'Permission requise',
          'Nous avons besoin de la permission d\'accéder à votre caméra.'
        );
        return;
      }
    }

    try {
      const result = await ImagePicker.launchCameraAsync({
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
        base64: true,
      });

      if (!result.canceled && result.assets[0]) {
        setSelectedImage(result.assets[0]);
      }
    } catch (error) {
      Alert.alert('Erreur', 'Erreur lors de la prise de photo');
    }
  };

  // Télécharger la photo sélectionnée
  const uploadPhoto = async () => {
    if (!selectedImage || !learnerId) return;

    setUploading(true);
    try {
      // Convertir l'image en base64 si nécessaire
      const base64Image = selectedImage.base64 || selectedImage.uri;
      
      await uploadDocument({
        variables: {
          learnerId,
          documentType: 'photo',
          file: `data:image/jpeg;base64,${base64Image}`
        }
      });
    } catch (error) {
      console.error('Upload error:', error);
      setUploading(false);
    }
  };

  // Supprimer la photo sélectionnée
  const removeSelectedImage = () => {
    setSelectedImage(null);
  };

  const getStatusInfo = (status) => {
    const statusMap = {
      'EN_ATTENTE': { label: 'En attente', color: '#FF9800', icon: 'clock' },
      'ACCEPTE': { label: 'Acceptée', color: '#4CAF50', icon: 'check' },
      'REJETE': { label: 'Rejetée', color: '#F44336', icon: 'close' }
    };
    return statusMap[status] || statusMap['EN_ATTENTE'];
  };

  const currentPhotoStatus = currentPhoto?.status;
  const statusInfo = getStatusInfo(currentPhotoStatus);

  return (
    <Card style={styles.card}>
      <Card.Content>
        <Title style={styles.title}>Photo d'identité</Title>
        
        {/* Photo actuelle */}
        {currentPhoto?.file && (
          <View style={styles.currentPhotoSection}>
            <Text style={styles.sectionTitle}>Photo actuelle</Text>
            <View style={styles.photoContainer}>
              <Image 
                source={{ uri: currentPhoto.file }} 
                style={styles.photoPreview}
                resizeMode="cover"
              />
              {showStatus && (
                <View style={styles.statusContainer}>
                  <Chip 
                    mode="outlined" 
                    style={[styles.statusChip, { borderColor: statusInfo.color }]}
                    textStyle={{ color: statusInfo.color }}
                    icon={statusInfo.icon}
                  >
                    {statusInfo.label}
                  </Chip>
                </View>
              )}
            </View>
            {currentPhoto.comment && (
              <Text style={styles.comment}>
                Commentaire : {currentPhoto.comment}
              </Text>
            )}
          </View>
        )}

        {/* Photo sélectionnée */}
        {selectedImage && (
          <View style={styles.selectedImageSection}>
            <Text style={styles.sectionTitle}>Nouvelle photo</Text>
            <View style={styles.photoContainer}>
              <Image 
                source={{ uri: selectedImage.uri }} 
                style={styles.photoPreview}
                resizeMode="cover"
              />
              <IconButton
                icon="close"
                size={24}
                style={styles.removeButton}
                onPress={removeSelectedImage}
              />
            </View>
          </View>
        )}

        {/* Instructions */}
        <View style={styles.instructionsSection}>
          <Text style={styles.instructionsTitle}>Instructions :</Text>
          <Text style={styles.instructionText}>• Photo récente et de bonne qualité</Text>
          <Text style={styles.instructionText}>• Fond neutre (blanc ou bleu clair)</Text>
          <Text style={styles.instructionText}>• Visage bien visible et centré</Text>
          <Text style={styles.instructionText}>• Format carré recommandé</Text>
          <Text style={styles.instructionText}>• Taille maximale : 5 MB</Text>
        </View>

        {/* Messages d'aide */}
        {!currentPhoto?.file && !selectedImage && (
          <HelperText type="info">
            Aucune photo téléchargée. Veuillez ajouter votre photo d'identité.
          </HelperText>
        )}

        {currentPhotoStatus === 'REJETE' && (
          <HelperText type="error">
            Votre photo a été rejetée. Veuillez en télécharger une nouvelle.
          </HelperText>
        )}
      </Card.Content>

      {/* Actions */}
      {editable && (
        <Card.Actions style={styles.actions}>
          {!selectedImage ? (
            <>
              <Button 
                mode="outlined" 
                onPress={pickImageFromGallery}
                icon="image"
                style={styles.actionButton}
              >
                Galerie
              </Button>
              <Button 
                mode="outlined" 
                onPress={takePhoto}
                icon="camera"
                style={styles.actionButton}
              >
                Caméra
              </Button>
            </>
          ) : (
            <>
              <Button 
                mode="outlined" 
                onPress={removeSelectedImage}
                disabled={uploading}
              >
                Annuler
              </Button>
              <Button 
                mode="contained" 
                onPress={uploadPhoto}
                loading={uploading}
                disabled={uploading}
                icon="upload"
              >
                Télécharger
              </Button>
            </>
          )}
        </Card.Actions>
      )}
    </Card>
  );
};

const styles = StyleSheet.create({
  card: {
    marginBottom: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  currentPhotoSection: {
    marginBottom: 16,
  },
  selectedImageSection: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
    color: '#333',
  },
  photoContainer: {
    position: 'relative',
    alignItems: 'center',
    marginBottom: 8,
  },
  photoPreview: {
    width: 150,
    height: 150,
    borderRadius: 8,
    borderWidth: 2,
    borderColor: '#ddd',
  },
  removeButton: {
    position: 'absolute',
    top: -8,
    right: -8,
    backgroundColor: '#F44336',
  },
  statusContainer: {
    marginTop: 8,
  },
  statusChip: {
    alignSelf: 'center',
  },
  comment: {
    fontSize: 14,
    color: '#666',
    fontStyle: 'italic',
    textAlign: 'center',
    marginTop: 8,
  },
  instructionsSection: {
    backgroundColor: '#f5f5f5',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  instructionsTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 8,
    color: '#333',
  },
  instructionText: {
    fontSize: 12,
    color: '#666',
    marginBottom: 2,
  },
  actions: {
    justifyContent: 'space-between',
    paddingHorizontal: 16,
  },
  actionButton: {
    flex: 1,
    marginHorizontal: 4,
  },
});

export default PhotoUploader;

import React, { useState } from 'react';
import { View, StyleSheet, ScrollView, Alert } from 'react-native';
import {
  Text,
  Card,
  Title,
  Button,
  ActivityIndicator,
  Chip,
  ProgressBar,
  List,
  Divider,
  Avatar,
  Menu
} from 'react-native-paper';
import { router, useLocalSearchParams } from 'expo-router';
import { useQuery } from '@apollo/client';
import { GET_LEARNER } from '../../services/graphql/queries';
import LearnerNavbar from '../../components/navigation/LearnerNavbar';

const LearnerStatus = {
  INSCRIT: { label: 'Inscrit', color: '#2196F3', icon: 'account-plus' },
  EN_COURS: { label: 'En cours', color: '#FF9800', icon: 'school' },
  TERMINE: { label: 'Terminé', color: '#4CAF50', icon: 'check-circle' },
  SUSPENDU: { label: 'Suspendu', color: '#F44336', icon: 'pause-circle' }
};

const DocumentStatus = {
  EN_ATTENTE: { label: 'En attente', color: '#FF9800', icon: 'clock' },
  ACCEPTE: { label: 'Accepté', color: '#4CAF50', icon: 'check' },
  REJETE: { label: 'Rejeté', color: '#F44336', icon: 'close' }
};

export default function LearnerDetailsScreen() {
  const { id } = useLocalSearchParams();
  const [menuVisible, setMenuVisible] = useState(false);

  const { data, loading, error, refetch } = useQuery(GET_LEARNER, {
    variables: { id },
    errorPolicy: 'all'
  });



  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" />
        <Text style={styles.loadingText}>Chargement des détails...</Text>
      </View>
    );
  }

  if (error || !data?.learner) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>
          {error?.message || 'Apprenant non trouvé'}
        </Text>
        <Button mode="contained" onPress={() => router.back()}>
          Retour
        </Button>
      </View>
    );
  }

  const learner = data.learner;
  const statusInfo = LearnerStatus[learner.status];

  const formatDate = (dateString) => {
    if (!dateString) return 'Non défini';
    return new Date(dateString).toLocaleDateString('fr-FR');
  };

  const getDocumentStatusInfo = (status) => {
    return DocumentStatus[status] || DocumentStatus.EN_ATTENTE;
  };

  return (
    <View style={styles.container}>
      {/* Custom Navbar */}
      <LearnerNavbar
        title={`${learner.user.firstName} ${learner.user.lastName}`}
        showBackButton={true}
      />

      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerInfo}>
          <Title style={styles.learnerName}>
            {learner.user.firstName} {learner.user.lastName}
          </Title>
          <Text style={styles.learnerSubtitle}>
            CIN: {learner.cin} • {learner.licenseType}
          </Text>
        </View>

        <View style={styles.headerActions}>
          <Chip
            mode="outlined"
            style={[styles.statusChip, { borderColor: statusInfo.color }]}
            textStyle={{ color: statusInfo.color }}
            icon={statusInfo.icon}
          >
            {statusInfo.label}
          </Chip>

          <Menu
            visible={menuVisible}
            onDismiss={() => setMenuVisible(false)}
            anchor={
              <Button
                mode="outlined"
                onPress={() => setMenuVisible(true)}
                icon="dots-vertical"
              >
                Actions
              </Button>
            }
          >
            <Menu.Item
              onPress={() => {
                setMenuVisible(false);
                router.push(`/learners/${id}/edit`);
              }}
              title="Modifier"
              leadingIcon="pencil"
            />
            <Menu.Item
              onPress={() => {
                setMenuVisible(false);
                router.push(`/learners/${id}/photo`);
              }}
              title="Gérer photo"
              leadingIcon="camera"
            />
            <Menu.Item
              onPress={() => {
                setMenuVisible(false);
                router.push(`/learners/${id}/documents`);
              }}
              title="Gérer documents"
              leadingIcon="file-document"
            />
            <Menu.Item
              onPress={() => {
                setMenuVisible(false);
                router.push(`/learners/${id}/fees`);
              }}
              title="Gérer frais"
              leadingIcon="currency-eur"
            />
          </Menu>
        </View>
      </View>

      <ScrollView style={styles.scrollView}>
        {/* Personal Information */}
        <Card style={styles.card}>
          <Card.Content>
            <Title>Informations personnelles</Title>
            <List.Item
              title="Email"
              description={learner.user.email}
              left={props => <List.Icon {...props} icon="email" />}
            />
            <List.Item
              title="Téléphone"
              description={learner.user.phone || 'Non renseigné'}
              left={props => <List.Icon {...props} icon="phone" />}
            />
            <List.Item
              title="Adresse"
              description={learner.user.address || 'Non renseignée'}
              left={props => <List.Icon {...props} icon="map-marker" />}
            />
            <List.Item
              title="Date de naissance"
              description={formatDate(learner.dateOfBirth)}
              left={props => <List.Icon {...props} icon="calendar" />}
            />
            <List.Item
              title="Date d'inscription"
              description={formatDate(learner.startDate)}
              left={props => <List.Icon {...props} icon="calendar-plus" />}
            />
            {learner.endDate && (
              <List.Item
                title="Date de fin"
                description={formatDate(learner.endDate)}
                left={props => <List.Icon {...props} icon="calendar-check" />}
              />
            )}
          </Card.Content>
        </Card>

        {/* Progress */}
        <Card style={styles.card}>
          <Card.Content>
            <Title>Progression</Title>

            <View style={styles.progressSection}>
              <Text style={styles.progressTitle}>Progression globale</Text>
              <View style={styles.progressRow}>
                <ProgressBar
                  progress={learner.progress / 100}
                  style={styles.progressBar}
                />
                <Text style={styles.progressText}>{Math.round(learner.progress)}%</Text>
              </View>
            </View>

            <View style={styles.progressSection}>
              <Text style={styles.progressTitle}>Théorie</Text>
              <View style={styles.progressRow}>
                <ProgressBar
                  progress={learner.progressDetails.theory.percentage / 100}
                  style={styles.progressBar}
                  color="#2196F3"
                />
                <Text style={styles.progressText}>
                  {learner.progressDetails.theory.percentage}%
                </Text>
              </View>
              <Text style={styles.progressDetails}>
                {learner.progressDetails.theory.lessonsCompleted} / {learner.progressDetails.theory.totalLessons} cours
              </Text>
            </View>

            <View style={styles.progressSection}>
              <Text style={styles.progressTitle}>Conduite</Text>
              <View style={styles.progressRow}>
                <ProgressBar
                  progress={learner.progressDetails.driving.percentage / 100}
                  style={styles.progressBar}
                  color="#FF9800"
                />
                <Text style={styles.progressText}>
                  {learner.progressDetails.driving.percentage}%
                </Text>
              </View>
              <Text style={styles.progressDetails}>
                {learner.progressDetails.driving.hoursCompleted} / {learner.progressDetails.driving.totalHours} heures
              </Text>
            </View>
          </Card.Content>
        </Card>

        {/* Documents */}
        <Card style={styles.card}>
          <Card.Content>
            <Title>Documents</Title>

            {Object.entries(learner.documents).map(([docType, docInfo]) => {
              const statusInfo = getDocumentStatusInfo(docInfo.status);
              const docLabels = {
                cin: 'Carte d\'identité',
                medicalCertificate: 'Certificat médical',
                photo: 'Photo d\'identité'
              };

              const docIcons = {
                cin: 'card-account-details',
                medicalCertificate: 'file-document-plus',
                photo: 'camera'
              };

              return (
                <List.Item
                  key={docType}
                  title={docLabels[docType]}
                  description={`${statusInfo.label} • ${formatDate(docInfo.uploadedAt)}`}
                  left={props => <List.Icon {...props} icon={docIcons[docType]} />}
                  right={() => (
                    <View style={styles.documentRight}>
                      {docType === 'photo' && docInfo.file && (
                        <Avatar.Image
                          size={40}
                          source={{ uri: docInfo.file }}
                          style={styles.photoPreview}
                        />
                      )}
                      <Chip
                        mode="outlined"
                        style={[styles.documentChip, { borderColor: statusInfo.color }]}
                        textStyle={{ color: statusInfo.color }}
                        icon={statusInfo.icon}
                      >
                        {statusInfo.label}
                      </Chip>
                    </View>
                  )}
                  onPress={() => {
                    if (docType === 'photo') {
                      router.push(`/learners/${id}/photo`);
                    } else {
                      router.push(`/learners/${id}/documents`);
                    }
                  }}
                />
              );
            })}
          </Card.Content>
          <Card.Actions>
            <Button
              mode="outlined"
              onPress={() => router.push(`/learners/${id}/photo`)}
              icon="camera"
              style={styles.actionButton}
            >
              Photo
            </Button>
            <Button
              mode="outlined"
              onPress={() => router.push(`/learners/${id}/documents`)}
              icon="file-document"
              style={styles.actionButton}
            >
              Documents
            </Button>
          </Card.Actions>
        </Card>

        {/* Fees */}
        <Card style={styles.card}>
          <Card.Content>
            <Title>Finances</Title>

            <View style={styles.feesGrid}>
              <View style={styles.feeItem}>
                <Text style={styles.feeLabel}>Total</Text>
                <Text style={styles.feeValue}>{learner.fees.total} DT</Text>
              </View>
              <View style={styles.feeItem}>
                <Text style={styles.feeLabel}>Payé</Text>
                <Text style={[styles.feeValue, { color: '#4CAF50' }]}>
                  {learner.fees.paid} DT
                </Text>
              </View>
              <View style={styles.feeItem}>
                <Text style={styles.feeLabel}>Reste</Text>
                <Text style={[styles.feeValue, { color: learner.fees.remaining > 0 ? '#F44336' : '#4CAF50' }]}>
                  {learner.fees.remaining} DT
                </Text>
              </View>
            </View>

            <Divider style={styles.divider} />

            <Text style={styles.feesBreakdownTitle}>Détail des frais</Text>
            <List.Item
              title="Heures de conduite"
              description={`${learner.fees.drivingHours} heures`}
              left={props => <List.Icon {...props} icon="car" />}
            />
            <List.Item
              title="Cours de théorie"
              description={`${learner.fees.theoryLessons} cours`}
              left={props => <List.Icon {...props} icon="book" />}
            />
            <List.Item
              title="Examen code"
              description={`${learner.fees.codeExam} examen(s)`}
              left={props => <List.Icon {...props} icon="clipboard-text" />}
            />
            <List.Item
              title="Examen conduite"
              description={`${learner.fees.drivingExam} examen(s)`}
              left={props => <List.Icon {...props} icon="car-brake-hold" />}
            />
            <List.Item
              title="Examen parking"
              description={`${learner.fees.parkingExam} examen(s)`}
              left={props => <List.Icon {...props} icon="parking" />}
            />
          </Card.Content>
          <Card.Actions>
            <Button
              mode="outlined"
              onPress={() => router.push(`/learners/${id}/fees`)}
            >
              Gérer les frais
            </Button>
          </Card.Actions>
        </Card>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: 'white',
    padding: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  headerInfo: {
    flex: 1,
  },
  learnerName: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  learnerSubtitle: {
    fontSize: 16,
    color: '#666',
  },
  headerActions: {
    alignItems: 'flex-end',
    gap: 8,
  },
  statusChip: {
    marginBottom: 8,
  },
  scrollView: {
    flex: 1,
    padding: 16,
  },
  card: {
    marginBottom: 16,
  },
  progressSection: {
    marginBottom: 16,
  },
  progressTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  progressRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  progressBar: {
    flex: 1,
    height: 8,
    marginRight: 12,
  },
  progressText: {
    fontSize: 16,
    fontWeight: 'bold',
    minWidth: 50,
    textAlign: 'right',
  },
  progressDetails: {
    fontSize: 14,
    color: '#666',
  },
  documentChip: {
    minWidth: 80,
  },
  documentRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  photoPreview: {
    borderWidth: 1,
    borderColor: '#ddd',
  },
  actionButton: {
    flex: 1,
    marginHorizontal: 4,
  },
  feesGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  feeItem: {
    alignItems: 'center',
  },
  feeLabel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  feeValue: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  divider: {
    marginVertical: 16,
  },
  feesBreakdownTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  errorText: {
    fontSize: 16,
    color: '#F44336',
    marginBottom: 16,
    textAlign: 'center',
  },
});

const { gql } = require('apollo-server-express');

const learnerTypeDefs = gql`
  type Learner {
    id: ID!
    user: User!
    dateOfBirth: Date!
    licenseType: LicenseType!
    medicalCertificate: String
    numberOfHours: Int!
    startDate: Date!
    endDate: Date
    progress: Float!
    status: LearnerStatus!
    cin: String!
    emergencyContact: String
    emergencyPhone: String
    medicalConditions: String
    notes: String
    documents: LearnerDocuments!
    fees: LearnerFees!
    progressDetails: LearnerProgressDetails!
    createdAt: DateTime!
    updatedAt: DateTime!
    sessions: [Session!]
    payments: [Payment!]
    courseProgress: [LearnerCourseProgress!]
    exams: [Exam!]
  }

  type LearnerDocuments {
    cin: DocumentInfo!
    medicalCertificate: DocumentInfo!
    photo: DocumentInfo!
  }

  type DocumentInfo {
    file: String
    status: DocumentStatus!
    comment: String
    uploadedAt: DateTime!
    validatedAt: DateTime
    validatedBy: User
  }

  type LearnerFees {
    drivingHours: Float!
    theoryLessons: Float!
    codeExam: Float!
    drivingExam: Float!
    parkingExam: Float!
    total: Float!
    paid: Float!
    remaining: Float!
  }

  type LearnerProgressDetails {
    theory: ProgressInfo!
    driving: ProgressInfo!
  }

  type ProgressInfo {
    percentage: Float!
    lessonsCompleted: Int
    totalLessons: Int
    hoursCompleted: Float
    totalHours: Float
  }

  enum DocumentStatus {
    EN_ATTENTE
    ACCEPTE
    REJETE
  }

  enum LearnerStatus {
    INSCRIT
    EN_COURS
    TERMINE
    SUSPENDU
  }

  input LearnerInput {
    userId: ID!
    dateOfBirth: Date!
    licenseType: LicenseType!
    medicalCertificate: String
    numberOfHours: Int
    startDate: Date
    endDate: Date
    progress: Float
    status: LearnerStatus
    cin: String!
    emergencyContact: String
    emergencyPhone: String
    medicalConditions: String
    notes: String
  }

  input LearnerUpdateInput {
    dateOfBirth: Date
    licenseType: LicenseType
    medicalCertificate: String
    numberOfHours: Int
    startDate: Date
    endDate: Date
    progress: Float
    status: LearnerStatus
    cin: String
    emergencyContact: String
    emergencyPhone: String
    medicalConditions: String
    notes: String
  }

  input DocumentValidationInput {
    learnerId: ID!
    documentType: String! # 'cin', 'medicalCertificate', 'photo'
    status: DocumentStatus!
    comment: String
  }

  input LearnerFeesInput {
    learnerId: ID!
    drivingHours: Float
    theoryLessons: Float
    codeExam: Float
    drivingExam: Float
    parkingExam: Float
  }

  input LearnerProgressInput {
    learnerId: ID!
    theoryPercentage: Float
    theoryLessonsCompleted: Int
    theoryTotalLessons: Int
    drivingPercentage: Float
    drivingHoursCompleted: Float
    drivingTotalHours: Float
  }

  extend type Query {
    learners: [Learner!]!
    learner(id: ID!): Learner
    learnerByUserId(userId: ID!): Learner
    learnerProgress(id: ID!): Float!
    learnerSessions(id: ID!): [Session!]!
    learnerPayments(id: ID!): [Payment!]!
    learnersWithPendingDocuments: [Learner!]!
    learnerDocumentsStatus(id: ID!): LearnerDocuments!
    learnerFees(id: ID!): LearnerFees!
  }

  extend type Mutation {
    createLearner(input: LearnerInput!): Learner!
    updateLearner(id: ID!, input: LearnerUpdateInput!): Learner!
    deleteLearner(id: ID!): Boolean!
    validateDocument(input: DocumentValidationInput!): Learner!
    uploadDocument(learnerId: ID!, documentType: String!, file: String!): Learner!
    updateLearnerFees(input: LearnerFeesInput!): Learner!
    updateLearnerProgress(input: LearnerProgressInput!): Learner!
    calculateLearnerFees(id: ID!): LearnerFees!
  }
`;

module.exports = { learnerTypeDefs };

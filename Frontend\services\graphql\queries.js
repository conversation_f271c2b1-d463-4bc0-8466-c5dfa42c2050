import { gql } from '@apollo/client';

// User queries
export const GET_USER = gql`
  query GetUser($id: ID!) {
    user(id: $id) {
      id
      email
      firstName
      lastName
      phone
      address
      photo
      role
      createdAt
      updatedAt
    }
  }
`;

export const GET_CURRENT_USER = gql`
  query GetCurrentUser {
    me {
      id
      email
      firstName
      lastName
      phone
      address
      photo
      role
      createdAt
      updatedAt
      learner {
        id
        status
        progress
        licenseType
        dateOfBirth
        numberOfHours
        startDate
        endDate
        progressDetails {
          theory {
            percentage
            lessonsCompleted
            totalLessons
          }
          driving {
            percentage
            hoursCompleted
            totalHours
          }
        }
        fees {
          total
          paid
          remaining
        }
        documents {
          cin {
            status
          }
          medicalCertificate {
            status
          }
          photo {
            status
          }
        }
      }
    }
  }
`;

export const GET_USERS = gql`
  query GetUsers {
    users {
      id
      email
      firstName
      lastName
      phone
      role
      createdAt
    }
  }
`;

// Learner queries
export const GET_LEARNER = gql`
  query GetLearner($id: ID!) {
    learner(id: $id) {
      id
      licenseType
      status
      progress
      cin
      dateOfBirth
      numberOfHours
      startDate
      endDate
      user {
        id
        firstName
        lastName
        email
        phone
        address
      }
      documents {
        cin {
          file
          status
          comment
          uploadedAt
          validatedAt
          validatedBy {
            firstName
            lastName
          }
        }
        medicalCertificate {
          file
          status
          comment
          uploadedAt
          validatedAt
          validatedBy {
            firstName
            lastName
          }
        }
        photo {
          file
          status
          comment
          uploadedAt
          validatedAt
          validatedBy {
            firstName
            lastName
          }
        }
      }
      fees {
        drivingHours
        theoryLessons
        codeExam
        drivingExam
        parkingExam
        total
        paid
        remaining
      }
      progressDetails {
        theory {
          percentage
          lessonsCompleted
          totalLessons
        }
        driving {
          percentage
          hoursCompleted
          totalHours
        }
      }
      createdAt
      updatedAt
    }
  }
`;

// Get all learners
export const GET_LEARNERS = gql`
  query GetLearners {
    learners {
      id
      licenseType
      status
      progress
      cin
      dateOfBirth
      numberOfHours
      startDate
      endDate
      user {
        id
        firstName
        lastName
        email
        phone
      }
      documents {
        cin {
          status
        }
        medicalCertificate {
          status
        }
        photo {
          status
        }
      }
      fees {
        total
        paid
        remaining
      }
      progressDetails {
        theory {
          percentage
        }
        driving {
          percentage
        }
      }
      createdAt
    }
  }
`;

// Session queries
export const GET_SESSIONS = gql`
  query GetSessions {
    sessions {
      id
      title
      description
      startTime
      endTime
      status
      type
      learner {
        id
        user {
          firstName
          lastName
        }
      }
      instructor {
        id
        employee {
          user {
            firstName
            lastName
          }
        }
      }
      vehicle {
        id
        brand
        model
        licensePlate
      }
    }
  }
`;

export const GET_LEARNER_SESSIONS = gql`
  query GetLearnerSessions($learnerId: ID!) {
    sessionsByLearner(learnerId: $learnerId) {
      id
      title
      description
      startTime
      endTime
      status
      type
      instructor {
        id
        employee {
          user {
            firstName
            lastName
          }
        }
      }
      vehicle {
        id
        brand
        model
        licensePlate
      }
    }
  }
`;

// Course queries
export const GET_COURSES = gql`
  query GetCourses {
    courses {
      id
      title
      description
      type
      category
      duration
      instructor {
        id
        employee {
          user {
            firstName
            lastName
          }
        }
      }
    }
  }
`;

// Exam queries
export const GET_LEARNER_EXAMS = gql`
  query GetLearnerExams($learnerId: ID!) {
    examsByLearner(learnerId: $learnerId) {
      id
      title
      description
      date
      examType
      result
      score
      notes
      instructor {
        id
        employee {
          user {
            firstName
            lastName
          }
        }
      }
    }
  }
`;

import React from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import { 
  Text, 
  Card, 
  Title, 
  ActivityIndicator,
  Button,
  Appbar
} from 'react-native-paper';
import { router } from 'expo-router';
import { useQuery } from '@apollo/client';
import { GET_CURRENT_USER } from '../services/graphql/queries';
import PhotoUploader from '../components/learners/PhotoUploader';

export default function MyPhotoScreen() {
  const { data, loading, error, refetch } = useQuery(GET_CURRENT_USER, {
    errorPolicy: 'all'
  });

  const handlePhotoUploaded = () => {
    // Actualiser les données après upload
    refetch();
  };

  if (loading) {
    return (
      <View style={styles.container}>
        <Appbar.Header style={styles.header}>
          <Appbar.BackAction onPress={() => router.back()} iconColor="#fff" />
          <Appbar.Content title="Ma photo d'identité" titleStyle={styles.headerTitle} />
        </Appbar.Header>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" />
          <Text style={styles.loadingText}>Chargement...</Text>
        </View>
      </View>
    );
  }

  if (error || !data?.me) {
    return (
      <View style={styles.container}>
        <Appbar.Header style={styles.header}>
          <Appbar.BackAction onPress={() => router.back()} iconColor="#fff" />
          <Appbar.Content title="Ma photo d'identité" titleStyle={styles.headerTitle} />
        </Appbar.Header>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>
            {error?.message || 'Erreur lors du chargement de vos informations'}
          </Text>
          <Button mode="contained" onPress={() => router.back()}>
            Retour
          </Button>
        </View>
      </View>
    );
  }

  const user = data.me;
  
  // Trouver les informations de l'apprenant
  // Note: Nous devrons adapter cela selon la structure de vos données
  const learnerInfo = user.learnerProfile; // Supposons que cette relation existe

  if (!learnerInfo) {
    return (
      <View style={styles.container}>
        <Appbar.Header style={styles.header}>
          <Appbar.BackAction onPress={() => router.back()} iconColor="#fff" />
          <Appbar.Content title="Ma photo d'identité" titleStyle={styles.headerTitle} />
        </Appbar.Header>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>
            Vous n'êtes pas enregistré comme apprenant.
          </Text>
          <Button mode="contained" onPress={() => router.back()}>
            Retour
          </Button>
        </View>
      </View>
    );
  }

  const photoDocument = learnerInfo.documents?.photo;

  return (
    <View style={styles.container}>
      <Appbar.Header style={styles.header}>
        <Appbar.BackAction onPress={() => router.back()} iconColor="#fff" />
        <Appbar.Content title="Ma photo d'identité" titleStyle={styles.headerTitle} />
        <Appbar.Action 
          icon="help-circle" 
          iconColor="#fff"
          onPress={() => {
            // Afficher l'aide ou les instructions
          }}
        />
      </Appbar.Header>
      
      <ScrollView style={styles.scrollContainer}>
        {/* Message de bienvenue */}
        <Card style={styles.welcomeCard}>
          <Card.Content>
            <Title>Bonjour {user.firstName} !</Title>
            <Text style={styles.welcomeText}>
              Téléchargez votre photo d'identité pour compléter votre dossier d'inscription.
            </Text>
          </Card.Content>
        </Card>

        {/* Statut du dossier */}
        <Card style={styles.statusCard}>
          <Card.Content>
            <Title>Statut de votre dossier</Title>
            <View style={styles.statusRow}>
              <Text style={styles.statusLabel}>Statut actuel :</Text>
              <Text style={[styles.statusValue, getStatusStyle(learnerInfo.status)]}>
                {getStatusLabel(learnerInfo.status)}
              </Text>
            </View>
            <View style={styles.statusRow}>
              <Text style={styles.statusLabel}>CIN :</Text>
              <Text style={styles.statusValue}>{learnerInfo.cin}</Text>
            </View>
            <View style={styles.statusRow}>
              <Text style={styles.statusLabel}>Type de permis :</Text>
              <Text style={styles.statusValue}>{learnerInfo.licenseType}</Text>
            </View>
          </Card.Content>
        </Card>

        {/* Composant de téléchargement de photo */}
        <PhotoUploader
          learnerId={learnerInfo.id}
          currentPhoto={photoDocument}
          onPhotoUploaded={handlePhotoUploaded}
          editable={true}
          showStatus={true}
        />

        {/* Message d'encouragement */}
        <Card style={styles.encouragementCard}>
          <Card.Content>
            <Title>Prochaines étapes</Title>
            <Text style={styles.encouragementText}>
              Une fois votre photo validée, vous pourrez :
            </Text>
            <Text style={styles.stepText}>• Commencer vos cours de théorie</Text>
            <Text style={styles.stepText}>• Réserver vos heures de conduite</Text>
            <Text style={styles.stepText}>• Suivre votre progression</Text>
            <Text style={styles.stepText}>• Passer vos examens</Text>
          </Card.Content>
        </Card>
      </ScrollView>
    </View>
  );
}

// Fonctions utilitaires
function getStatusLabel(status) {
  const labels = {
    'INSCRIT': 'Inscrit',
    'EN_COURS': 'Formation en cours',
    'TERMINE': 'Formation terminée',
    'SUSPENDU': 'Suspendu'
  };
  return labels[status] || status;
}

function getStatusStyle(status) {
  const styles = {
    'INSCRIT': { color: '#2196F3' },
    'EN_COURS': { color: '#FF9800' },
    'TERMINE': { color: '#4CAF50' },
    'SUSPENDU': { color: '#F44336' }
  };
  return styles[status] || { color: '#666' };
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#6200ee',
  },
  headerTitle: {
    color: '#fff',
    fontWeight: 'bold',
  },
  scrollContainer: {
    flex: 1,
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  errorText: {
    fontSize: 16,
    color: '#F44336',
    marginBottom: 16,
    textAlign: 'center',
  },
  welcomeCard: {
    marginBottom: 16,
    backgroundColor: '#e8f5e8',
  },
  welcomeText: {
    fontSize: 16,
    color: '#2e7d32',
    marginTop: 8,
  },
  statusCard: {
    marginBottom: 16,
  },
  statusRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  statusLabel: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
  },
  statusValue: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  encouragementCard: {
    marginBottom: 16,
    backgroundColor: '#fff3e0',
  },
  encouragementText: {
    fontSize: 14,
    color: '#e65100',
    marginBottom: 12,
  },
  stepText: {
    fontSize: 14,
    color: '#bf360c',
    marginBottom: 4,
    marginLeft: 8,
  },
});

import React from 'react';
import { View, Text, ScrollView, StyleSheet } from 'react-native';
import { Card, Button } from 'react-native-paper';

export default function TestScrollScreen() {
  return (
    <View style={styles.container}>
      <ScrollView 
        style={styles.scrollView}
        contentContainerStyle={styles.contentContainer}
        showsVerticalScrollIndicator={true}
      >
        <Card style={styles.card}>
          <Card.Content>
            <Text style={styles.title}>Test de défilement</Text>
            
            {/* Générer beaucoup de contenu pour tester le scroll */}
            {Array.from({ length: 50 }, (_, index) => (
              <View key={index} style={styles.item}>
                <Text style={styles.itemText}>
                  Élément {index + 1} - Ce texte devrait être scrollable
                </Text>
              </View>
            ))}
            
            <Button mode="contained" style={styles.button}>
              Bouton en bas de page
            </Button>
          </Card.Content>
        </Card>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollView: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
    paddingBottom: 100,
  },
  card: {
    marginBottom: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  item: {
    backgroundColor: '#e3f2fd',
    padding: 12,
    marginBottom: 8,
    borderRadius: 8,
  },
  itemText: {
    fontSize: 16,
    color: '#333',
  },
  button: {
    marginTop: 20,
  },
});

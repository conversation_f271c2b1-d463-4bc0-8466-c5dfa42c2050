import React, { useState } from 'react';
import { View, StyleSheet, ScrollView, RefreshControl } from 'react-native';
import {
  Text,
  Card,
  Title,
  Button,
  ActivityIndicator,
  Searchbar,
  Chip,
  List,
  Avatar,
  FAB,
  Menu,
  Divider
} from 'react-native-paper';
import { router } from 'expo-router';
import { useQuery } from '@apollo/client';
import { GET_LEARNERS } from '../../services/graphql/queries';
import LearnerNavbar from '../../components/navigation/LearnerNavbar';

const LearnerStatus = {
  INSCRIT: { label: 'Inscrit', color: '#2196F3' },
  EN_COURS: { label: 'En cours', color: '#FF9800' },
  TERMINE: { label: 'Terminé', color: '#4CAF50' },
  SUSPENDU: { label: 'Suspendu', color: '#F44336' }
};

const DocumentStatus = {
  EN_ATTENTE: { label: 'En attente', color: '#FF9800' },
  ACCEPTE: { label: 'Accepté', color: '#4CAF50' },
  REJETE: { label: 'Rejeté', color: '#F44336' }
};

export default function LearnersListScreen() {
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('ALL');
  const [menuVisible, setMenuVisible] = useState(false);

  const { data, loading, error, refetch } = useQuery(GET_LEARNERS, {
    errorPolicy: 'all'
  });

  const learners = data?.learners || [];

  // Filter learners based on search and status
  const filteredLearners = learners.filter(learner => {
    const matchesSearch =
      learner.user.firstName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      learner.user.lastName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      learner.cin.toLowerCase().includes(searchQuery.toLowerCase()) ||
      learner.user.email.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesStatus = statusFilter === 'ALL' || learner.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  const getDocumentsStatusSummary = (documents) => {
    const statuses = [
      documents.cin.status,
      documents.medicalCertificate.status,
      documents.photo.status
    ];

    const accepted = statuses.filter(s => s === 'ACCEPTE').length;
    const pending = statuses.filter(s => s === 'EN_ATTENTE').length;
    const rejected = statuses.filter(s => s === 'REJETE').length;

    return { accepted, pending, rejected, total: 3 };
  };

  const renderLearnerCard = (learner) => {
    const documentsStatus = getDocumentsStatusSummary(learner.documents);
    const statusInfo = LearnerStatus[learner.status];

    return (
      <Card key={learner.id} style={styles.learnerCard}>
        <Card.Content>
          <View style={styles.learnerHeader}>
            <View style={styles.learnerInfo}>
              <Title style={styles.learnerName}>
                {learner.user.firstName} {learner.user.lastName}
              </Title>
              <Text style={styles.learnerDetails}>
                CIN: {learner.cin} • {learner.licenseType}
              </Text>
              <Text style={styles.learnerDetails}>
                {learner.user.email} • {learner.user.phone}
              </Text>
            </View>
            <View style={styles.statusContainer}>
              <Chip
                mode="outlined"
                style={[styles.statusChip, { borderColor: statusInfo.color }]}
                textStyle={{ color: statusInfo.color }}
              >
                {statusInfo.label}
              </Chip>
            </View>
          </View>

          <View style={styles.progressSection}>
            <Text style={styles.sectionTitle}>Progression</Text>
            <View style={styles.progressRow}>
              <View style={styles.progressItem}>
                <Text style={styles.progressLabel}>Théorie</Text>
                <Text style={styles.progressValue}>
                  {learner.progressDetails.theory.percentage}%
                </Text>
              </View>
              <View style={styles.progressItem}>
                <Text style={styles.progressLabel}>Conduite</Text>
                <Text style={styles.progressValue}>
                  {learner.progressDetails.driving.percentage}%
                </Text>
              </View>
              <View style={styles.progressItem}>
                <Text style={styles.progressLabel}>Global</Text>
                <Text style={styles.progressValue}>
                  {Math.round(learner.progress)}%
                </Text>
              </View>
            </View>
          </View>

          <View style={styles.documentsSection}>
            <Text style={styles.sectionTitle}>Documents</Text>
            <View style={styles.documentsRow}>
              <Chip
                mode="outlined"
                style={[styles.documentChip, { borderColor: '#4CAF50' }]}
                textStyle={{ color: '#4CAF50' }}
                icon="check"
              >
                {documentsStatus.accepted}
              </Chip>
              <Chip
                mode="outlined"
                style={[styles.documentChip, { borderColor: '#FF9800' }]}
                textStyle={{ color: '#FF9800' }}
                icon="clock"
              >
                {documentsStatus.pending}
              </Chip>
              <Chip
                mode="outlined"
                style={[styles.documentChip, { borderColor: '#F44336' }]}
                textStyle={{ color: '#F44336' }}
                icon="close"
              >
                {documentsStatus.rejected}
              </Chip>
            </View>
          </View>

          <View style={styles.feesSection}>
            <Text style={styles.sectionTitle}>Finances</Text>
            <View style={styles.feesRow}>
              <Text style={styles.feesText}>
                Total: {learner.fees.total} DT
              </Text>
              <Text style={styles.feesText}>
                Payé: {learner.fees.paid} DT
              </Text>
              <Text style={[styles.feesText, { color: learner.fees.remaining > 0 ? '#F44336' : '#4CAF50' }]}>
                Reste: {learner.fees.remaining} DT
              </Text>
            </View>
          </View>
        </Card.Content>

        <Card.Actions>
          <Button
            mode="outlined"
            onPress={() => router.push(`/learners/${learner.id}`)}
          >
            Voir détails
          </Button>
          <Button
            mode="contained"
            onPress={() => router.push(`/learners/${learner.id}/edit`)}
          >
            Modifier
          </Button>
        </Card.Actions>
      </Card>
    );
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" />
        <Text style={styles.loadingText}>Chargement des apprenants...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>Erreur lors du chargement des apprenants</Text>
        <Button mode="contained" onPress={() => refetch()}>
          Réessayer
        </Button>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Custom Navbar */}
      <LearnerNavbar
        title="Liste des apprenants"
        showBackButton={false}
        learnerCount={filteredLearners.length}
      />

      {/* Header with search and filter */}
      <View style={styles.header}>
        <Searchbar
          placeholder="Rechercher un apprenant..."
          onChangeText={setSearchQuery}
          value={searchQuery}
          style={styles.searchbar}
        />

        <Menu
          visible={menuVisible}
          onDismiss={() => setMenuVisible(false)}
          anchor={
            <Button
              mode="outlined"
              onPress={() => setMenuVisible(true)}
              icon="filter"
            >
              {statusFilter === 'ALL' ? 'Tous' : LearnerStatus[statusFilter]?.label}
            </Button>
          }
        >
          <Menu.Item onPress={() => { setStatusFilter('ALL'); setMenuVisible(false); }} title="Tous" />
          <Divider />
          {Object.entries(LearnerStatus).map(([key, value]) => (
            <Menu.Item
              key={key}
              onPress={() => { setStatusFilter(key); setMenuVisible(false); }}
              title={value.label}
            />
          ))}
        </Menu>
      </View>

      {/* Learners list */}
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={loading} onRefresh={refetch} />
        }
      >
        <View style={styles.statsContainer}>
          <Text style={styles.statsText}>
            {filteredLearners.length} apprenant(s) trouvé(s)
          </Text>
        </View>

        {filteredLearners.map(renderLearnerCard)}
      </ScrollView>

      {/* Floating Action Button */}
      <FAB
        style={styles.fab}
        icon="plus"
        onPress={() => router.push('/learners/create')}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    padding: 16,
    backgroundColor: 'white',
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  searchbar: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
    padding: 16,
  },
  statsContainer: {
    marginBottom: 16,
  },
  statsText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#666',
  },
  learnerCard: {
    marginBottom: 16,
  },
  learnerHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  learnerInfo: {
    flex: 1,
  },
  learnerName: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  learnerDetails: {
    fontSize: 14,
    color: '#666',
    marginBottom: 2,
  },
  statusContainer: {
    alignItems: 'flex-end',
  },
  statusChip: {
    marginBottom: 8,
  },
  progressSection: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
    color: '#333',
  },
  progressRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  progressItem: {
    alignItems: 'center',
  },
  progressLabel: {
    fontSize: 12,
    color: '#666',
  },
  progressValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2196F3',
  },
  documentsSection: {
    marginBottom: 16,
  },
  documentsRow: {
    flexDirection: 'row',
    gap: 8,
  },
  documentChip: {
    minWidth: 50,
  },
  feesSection: {
    marginBottom: 8,
  },
  feesRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  feesText: {
    fontSize: 14,
    fontWeight: '500',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  errorText: {
    fontSize: 16,
    color: '#F44336',
    marginBottom: 16,
    textAlign: 'center',
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
  },
});

import React, { useMemo } from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import { Text, Card, Title, Paragraph, Button, Surface, ActivityIndicator, Avatar, List } from 'react-native-paper';
import { router } from 'expo-router';
import { useSessions, filterUpcomingSessions } from '../services/sessionService';

export default function AdminHome({ user }) {
  // Utiliser directement user au lieu de currentUser
  // Get sessions
  const { sessions, loading: sessionsLoading, error: sessionsError } = useSessions();

  // Utiliser useMemo pour éviter les recalculs inutiles
  const upcomingSessions = useMemo(() => {
    return filterUpcomingSessions(sessions || []).slice(0, 3);
  }, [sessions]);

  if (sessionsLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" />
        <Text style={styles.loadingText}>Chargement...</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      <Surface style={styles.welcomeCard}>
        <Text style={styles.welcomeText}>
          Bienvenue, {user?.firstName} {user?.lastName}
        </Text>
        <Text style={styles.roleText}>Administrateur</Text>
      </Surface>

      {/* Tableau de bord administrateur */}
      <View style={styles.dashboardContainer}>
        <Card style={styles.dashboardCard}>
          <Card.Content>
            <Title>Tableau de bord</Title>
            <View style={styles.statsContainer}>
              <View style={styles.statItem}>
                <Avatar.Icon size={40} icon="account-group" style={styles.statIcon} />
                <Text style={styles.statValue}>24</Text>
                <Text style={styles.statLabel}>Apprenants</Text>
              </View>
              <View style={styles.statItem}>
                <Avatar.Icon size={40} icon="teach" style={styles.statIcon} />
                <Text style={styles.statValue}>8</Text>
                <Text style={styles.statLabel}>Moniteurs</Text>
              </View>
              <View style={styles.statItem}>
                <Avatar.Icon size={40} icon="car" style={styles.statIcon} />
                <Text style={styles.statValue}>12</Text>
                <Text style={styles.statLabel}>Véhicules</Text>
              </View>
            </View>
          </Card.Content>
        </Card>
      </View>

      {/* Actions rapides */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Actions rapides</Text>
        <Card style={styles.card}>
          <Card.Content>
            <View style={styles.quickActionsContainer}>
              <Button
                mode="contained"
                icon="school"
                style={styles.actionButton}
                onPress={() => router.push('/learners')}
              >
                Gérer apprenants
              </Button>
              <Button
                mode="contained"
                icon="account-plus"
                style={styles.actionButton}
                onPress={() => router.push('/learners/create')}
              >
                Nouvel apprenant
              </Button>
              <Button
                mode="contained"
                icon="calendar-plus"
                style={styles.actionButton}
                onPress={() => router.push('/sessions/new')}
              >
                Nouvelle séance
              </Button>
              <Button
                mode="contained"
                icon="car-multiple"
                style={styles.actionButton}
                onPress={() => router.push('/vehicles')}
              >
                Gérer véhicules
              </Button>
            </View>
          </Card.Content>
        </Card>
      </View>

      {/* Prochaines séances */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Prochaines séances</Text>

        {sessionsError ? (
          <Text style={styles.errorText}>Erreur de chargement des séances</Text>
        ) : upcomingSessions.length === 0 ? (
          <Text style={styles.noDataText}>Aucune séance à venir</Text>
        ) : (
          upcomingSessions.map(session => (
            <Card key={session.id} style={styles.card}>
              <Card.Content>
                <Title>{session.title}</Title>
                <Paragraph>{session.description}</Paragraph>
                <View style={styles.sessionDetails}>
                  <Text style={styles.sessionDate}>
                    {new Date(session.startTime).toLocaleDateString()} à {new Date(session.startTime).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                  </Text>
                  <Text style={styles.sessionStatus}>
                    {session.status === 'PLANIFIE' ? 'Planifiée' :
                     session.status === 'TERMINE' ? 'Terminée' :
                     session.status === 'ANNULE' ? 'Annulée' : session.status}
                  </Text>
                </View>
              </Card.Content>
              <Card.Actions>
                <Button onPress={() => router.push(`/sessions/${session.id}`)}>
                  Détails
                </Button>
              </Card.Actions>
            </Card>
          ))
        )}

        <Button
          mode="outlined"
          style={styles.viewAllButton}
          onPress={() => router.push('/sessions')}
        >
          Voir toutes les séances
        </Button>
      </View>

      {/* Activité récente */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Activité récente</Text>
        <Card style={styles.card}>
          <Card.Content>
            <List.Item
              title="Nouvel apprenant inscrit"
              description="Sophie Martin s'est inscrite"
              left={props => <List.Icon {...props} icon="account-plus" />}
              right={props => <Text>Il y a 2h</Text>}
            />
            <List.Item
              title="Paiement reçu"
              description="Jean Dupont - Forfait 20h"
              left={props => <List.Icon {...props} icon="cash-multiple" />}
              right={props => <Text>Il y a 3h</Text>}
            />
            <List.Item
              title="Séance annulée"
              description="Conduite - Thomas Petit"
              left={props => <List.Icon {...props} icon="calendar-remove" />}
              right={props => <Text>Il y a 5h</Text>}
            />
          </Card.Content>
          <Card.Actions>
            <Button onPress={() => router.push('/notifications')}>
              Voir toutes les activités
            </Button>
          </Card.Actions>
        </Card>
      </View>


    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
  },
  welcomeCard: {
    padding: 20,
    borderRadius: 10,
    marginBottom: 20,
    elevation: 4,
  },
  welcomeText: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  roleText: {
    fontSize: 16,
    color: '#666',
    marginTop: 5,
  },
  dashboardContainer: {
    marginBottom: 20,
  },
  dashboardCard: {
    marginBottom: 10,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 10,
  },
  statItem: {
    alignItems: 'center',
    flex: 1,
  },
  statIcon: {
    backgroundColor: '#3f51b5',
  },
  statValue: {
    fontSize: 20,
    fontWeight: 'bold',
    marginTop: 5,
  },
  statLabel: {
    color: '#666',
  },
  section: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  card: {
    marginBottom: 10,
  },
  quickActionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  actionButton: {
    marginBottom: 10,
    width: '48%',
    minWidth: 140,
  },
  sessionDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 10,
  },
  sessionDate: {
    color: '#666',
  },
  sessionStatus: {
    fontWeight: 'bold',
  },
  noDataText: {
    textAlign: 'center',
    marginVertical: 20,
    color: '#666',
  },
  viewAllButton: {
    marginTop: 10,
  },
  errorText: {
    color: 'red',
    marginBottom: 10,
  },

});

import React, { useState } from 'react';
import { View, StyleSheet } from 'react-native';
import { 
  Appbar, 
  <PERSON>u, 
  Divider,
  Badge
} from 'react-native-paper';
import { router, usePathname } from 'expo-router';

export default function LearnerNavbar({ title, showBackButton = true, learnerCount = 0 }) {
  const [menuVisible, setMenuVisible] = useState(false);
  const pathname = usePathname();

  const isCreatePage = pathname.includes('/create');
  const isListPage = pathname === '/learners';
  const isDetailPage = pathname.includes('/learners/') && !pathname.includes('/create') && !pathname.includes('/edit');

  const handleMenuAction = (action) => {
    setMenuVisible(false);
    
    switch (action) {
      case 'home':
        router.push('/');
        break;
      case 'list':
        router.push('/learners');
        break;
      case 'create':
        router.push('/learners/create');
        break;
      case 'refresh':
        // Trigger refresh - this would need to be passed as a prop
        window.location.reload();
        break;
    }
  };

  return (
    <Appbar.Header style={styles.header}>
      {/* Back button */}
      {showBackButton && (
        <Appbar.BackAction 
          onPress={() => router.back()} 
          iconColor="#fff"
        />
      )}

      {/* Title with badge for learner count */}
      <Appbar.Content 
        title={title} 
        titleStyle={styles.title}
      />
      
      {isListPage && learnerCount > 0 && (
        <View style={styles.badgeContainer}>
          <Badge style={styles.badge}>{learnerCount}</Badge>
        </View>
      )}

      {/* Quick actions */}
      {!isCreatePage && (
        <Appbar.Action 
          icon="plus" 
          iconColor="#fff"
          onPress={() => router.push('/learners/create')}
          style={styles.actionButton}
        />
      )}

      {!isListPage && (
        <Appbar.Action 
          icon="format-list-bulleted" 
          iconColor="#fff"
          onPress={() => router.push('/learners')}
          style={styles.actionButton}
        />
      )}

      {/* Menu */}
      <Menu
        visible={menuVisible}
        onDismiss={() => setMenuVisible(false)}
        anchor={
          <Appbar.Action 
            icon="dots-vertical" 
            iconColor="#fff"
            onPress={() => setMenuVisible(true)}
          />
        }
        contentStyle={styles.menu}
      >
        <Menu.Item 
          onPress={() => handleMenuAction('home')} 
          title="Accueil" 
          leadingIcon="home"
        />
        <Divider />
        <Menu.Item 
          onPress={() => handleMenuAction('list')} 
          title="Liste des apprenants" 
          leadingIcon="format-list-bulleted"
          disabled={isListPage}
        />
        <Menu.Item 
          onPress={() => handleMenuAction('create')} 
          title="Créer un apprenant" 
          leadingIcon="plus"
          disabled={isCreatePage}
        />
        <Divider />
        <Menu.Item 
          onPress={() => handleMenuAction('refresh')} 
          title="Actualiser" 
          leadingIcon="refresh"
        />
      </Menu>
    </Appbar.Header>
  );
}

const styles = StyleSheet.create({
  header: {
    backgroundColor: '#6200ee',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  title: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 18,
  },
  actionButton: {
    marginHorizontal: 4,
  },
  badgeContainer: {
    position: 'relative',
    marginRight: 8,
  },
  badge: {
    backgroundColor: '#ff4444',
    color: '#fff',
    fontSize: 12,
    minWidth: 20,
    height: 20,
  },
  menu: {
    backgroundColor: '#fff',
    borderRadius: 8,
    marginTop: 8,
  },
});

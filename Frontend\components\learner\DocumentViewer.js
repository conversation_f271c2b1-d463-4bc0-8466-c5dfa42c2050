import React, { useState } from 'react';
import { View, StyleSheet, Image, Modal, TouchableOpacity } from 'react-native';
import {
  Text,
  Card,
  Surface,
  Chip,
  IconButton,
  Button
} from 'react-native-paper';
import { MaterialIcons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';

const DocumentViewer = ({ documents, userRole = 'APPRENANT' }) => {
  const router = useRouter();
  const [selectedDocument, setSelectedDocument] = useState(null);
  const [modalVisible, setModalVisible] = useState(false);

  // Configuration des types de documents
  const documentConfig = {
    cin: {
      title: 'Carte d\'identité nationale',
      description: 'Document d\'identité officiel',
      icon: 'credit-card',
      color: '#2196F3'
    },
    medicalCertificate: {
      title: 'Certificat médical',
      description: 'Certificat d\'aptitude à la conduite',
      icon: 'local-hospital',
      color: '#4CAF50'
    },
    photo: {
      title: 'Photo d\'identité',
      description: 'Photo d\'identité officielle',
      icon: 'photo-camera',
      color: '#FF9800'
    }
  };

  // Configuration des statuts
  const statusConfig = {
    EN_ATTENTE: {
      label: 'En attente',
      color: '#FF9800',
      backgroundColor: '#FFF3E0',
      icon: 'schedule'
    },
    ACCEPTE: {
      label: 'Accepté',
      color: '#4CAF50',
      backgroundColor: '#E8F5E8',
      icon: 'check-circle'
    },
    REFUSE: {
      label: 'Refusé',
      color: '#F44336',
      backgroundColor: '#FFEBEE',
      icon: 'cancel'
    }
  };

  const openDocument = (docType, document) => {
    setSelectedDocument({ type: docType, ...document });
    setModalVisible(true);
  };

  const closeModal = () => {
    setModalVisible(false);
    setSelectedDocument(null);
  };

  const renderDocument = (docType, document) => {
    const config = documentConfig[docType];
    const status = statusConfig[document?.status || 'EN_ATTENTE'];

    if (!document || !document.file) {
      return (
        <Card key={docType} style={styles.documentCard}>
          <Card.Content>
            <View style={styles.documentHeader}>
              <View style={styles.titleContainer}>
                <MaterialIcons
                  name={config.icon}
                  size={24}
                  color="#ccc"
                />
                <Text style={[styles.documentTitle, { color: '#ccc' }]}>
                  {config.title}
                </Text>
              </View>
              <Chip
                mode="flat"
                style={[styles.statusChip, { backgroundColor: '#f5f5f5' }]}
                textStyle={[styles.chipText, { color: '#999' }]}
                icon="help"
              >
                Non fourni
              </Chip>
            </View>
            <Text style={styles.documentDescription}>{config.description}</Text>
            <View style={styles.noDocumentContainer}>
              <MaterialIcons name="description" size={48} color="#e0e0e0" />
              <Text style={styles.noDocumentText}>Document non disponible</Text>
            </View>
          </Card.Content>
        </Card>
      );
    }

    return (
      <Card key={docType} style={styles.documentCard}>
        <Card.Content>
          <View style={styles.documentHeader}>
            <View style={styles.titleContainer}>
              <MaterialIcons
                name={config.icon}
                size={24}
                color={config.color}
              />
              <Text style={[styles.documentTitle, { color: config.color }]}>
                {config.title}
              </Text>
            </View>
            <Chip
              mode="flat"
              style={[styles.statusChip, { backgroundColor: status.backgroundColor }]}
              textStyle={[styles.chipText, { color: status.color }]}
              icon={status.icon}
            >
              {status.label}
            </Chip>
          </View>

          <Text style={styles.documentDescription}>{config.description}</Text>

          <TouchableOpacity
            style={styles.documentPreview}
            onPress={() => openDocument(docType, document)}
          >
            <Surface style={styles.previewContainer}>
              <Image
                source={{ uri: document.file }}
                style={styles.previewImage}
                resizeMode="cover"
              />
              <View style={styles.previewOverlay}>
                <MaterialIcons name="zoom-in" size={24} color="white" />
              </View>
            </Surface>
          </TouchableOpacity>

          <View style={styles.documentInfo}>
            <Text style={styles.uploadDate}>
              📅 Ajouté le : {new Date(document.uploadedAt).toLocaleDateString('fr-FR')}
            </Text>
            {document.comment && (
              <Text style={styles.comment}>
                💬 Commentaire : {document.comment}
              </Text>
            )}
          </View>

          <Button
            mode="outlined"
            onPress={() => openDocument(docType, document)}
            icon="visibility"
            style={styles.viewButton}
          >
            Voir le document
          </Button>
        </Card.Content>
      </Card>
    );
  };

  return (
    <View style={styles.container}>
      <Text style={styles.sectionTitle}>📄 État de mes documents</Text>
      <Text style={styles.sectionDescription}>
        Consultez l'état de validation de vos documents téléversés
      </Text>

      {/* Section d'aide */}
      <View style={styles.helpSection}>
        <Text style={styles.helpTitle}>ℹ️ À propos de vos documents</Text>
        <Text style={styles.helpText}>
          • Vous téléversez vos documents via la page de téléversement
        </Text>
        <Text style={styles.helpText}>
          • L'administration valide vos documents (accepté/rejeté)
        </Text>
        <Text style={styles.helpText}>
          • Vous pouvez consulter l'état de validation et les commentaires ici
        </Text>
        <Text style={styles.helpText}>
          • En cas de rejet, téléversez à nouveau le document corrigé
        </Text>
      </View>

      {/* Bouton pour téléverser des documents */}
      <View style={styles.uploadSection}>
        <Button
          mode="contained"
          onPress={() => router.push('/upload-documents')}
          icon="cloud-upload"
          style={styles.uploadButton}
        >
          Téléverser mes documents
        </Button>
        <Text style={styles.uploadDescription}>
          Téléversez ou mettez à jour vos documents d'inscription
        </Text>
      </View>

      {Object.entries(documentConfig).map(([docType, config]) =>
        renderDocument(docType, documents?.[docType])
      )}

      {/* Modal pour afficher le document en grand */}
      <Modal
        visible={modalVisible}
        transparent={true}
        animationType="fade"
        onRequestClose={closeModal}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>
                {selectedDocument && documentConfig[selectedDocument.type]?.title}
              </Text>
              <IconButton
                icon="close"
                size={24}
                onPress={closeModal}
              />
            </View>

            {selectedDocument && (
              <Image
                source={{ uri: selectedDocument.file }}
                style={styles.fullImage}
                resizeMode="contain"
              />
            )}

            <Button
              mode="contained"
              onPress={closeModal}
              style={styles.closeButton}
            >
              Fermer
            </Button>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  sectionDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 16,
    lineHeight: 20,
  },
  documentCard: {
    marginBottom: 16,
    elevation: 2,
  },
  documentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  documentTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  statusChip: {
    marginLeft: 8,
  },
  chipText: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  documentDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 16,
  },
  documentPreview: {
    alignItems: 'center',
    marginBottom: 16,
  },
  previewContainer: {
    position: 'relative',
    borderRadius: 8,
    overflow: 'hidden',
    elevation: 2,
  },
  previewImage: {
    width: 150,
    height: 100,
    borderRadius: 8,
  },
  previewOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  noDocumentContainer: {
    alignItems: 'center',
    padding: 20,
  },
  noDocumentText: {
    fontSize: 14,
    color: '#999',
    marginTop: 8,
  },
  documentInfo: {
    backgroundColor: '#f8f9fa',
    padding: 12,
    borderRadius: 8,
    marginBottom: 12,
  },
  uploadDate: {
    fontSize: 12,
    color: '#666',
    marginBottom: 4,
  },
  comment: {
    fontSize: 12,
    color: '#666',
    fontStyle: 'italic',
  },
  viewButton: {
    marginTop: 8,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    width: '90%',
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  fullImage: {
    width: '100%',
    height: 300,
    borderRadius: 8,
    marginBottom: 16,
  },
  closeButton: {
    marginTop: 8,
  },
  helpSection: {
    backgroundColor: '#e8f4fd',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
    borderLeftWidth: 4,
    borderLeftColor: '#2196F3',
  },
  helpTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#1976D2',
    marginBottom: 8,
  },
  helpText: {
    fontSize: 12,
    color: '#1976D2',
    marginBottom: 4,
    lineHeight: 16,
  },
  uploadSection: {
    backgroundColor: '#e8f5e8',
    padding: 16,
    borderRadius: 12,
    marginBottom: 20,
    alignItems: 'center',
    borderLeftWidth: 4,
    borderLeftColor: '#4CAF50',
  },
  uploadButton: {
    marginBottom: 8,
    backgroundColor: '#4CAF50',
  },
  uploadDescription: {
    fontSize: 12,
    color: '#2E7D32',
    textAlign: 'center',
    fontStyle: 'italic',
  },
});

export default DocumentViewer;

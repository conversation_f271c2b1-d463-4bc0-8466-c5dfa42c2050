import React from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import { 
  Text, 
  Card, 
  Title, 
  ActivityIndicator,
  Button
} from 'react-native-paper';
import { router, useLocalSearchParams } from 'expo-router';
import { useQuery } from '@apollo/client';
import { GET_LEARNER } from '../../../services/graphql/queries';
import LearnerNavbar from '../../../components/navigation/LearnerNavbar';
import PhotoUploader from '../../../components/learners/PhotoUploader';

export default function LearnerPhotoScreen() {
  const { id } = useLocalSearchParams();

  const { data, loading, error, refetch } = useQuery(GET_LEARNER, {
    variables: { id },
    errorPolicy: 'all'
  });

  const handlePhotoUploaded = (updatedLearner) => {
    // Actualiser les données après upload
    refetch();
  };

  if (loading) {
    return (
      <View style={styles.container}>
        <LearnerNavbar 
          title="Photo d'identité" 
          showBackButton={true}
        />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" />
          <Text style={styles.loadingText}>Chargement...</Text>
        </View>
      </View>
    );
  }

  if (error || !data?.learner) {
    return (
      <View style={styles.container}>
        <LearnerNavbar 
          title="Photo d'identité" 
          showBackButton={true}
        />
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>
            {error?.message || 'Apprenant non trouvé'}
          </Text>
          <Button mode="contained" onPress={() => router.back()}>
            Retour
          </Button>
        </View>
      </View>
    );
  }

  const learner = data.learner;
  const photoDocument = learner.documents?.photo;

  return (
    <View style={styles.container}>
      <LearnerNavbar 
        title={`Photo - ${learner.user.firstName} ${learner.user.lastName}`}
        showBackButton={true}
      />
      
      <ScrollView style={styles.scrollContainer}>
        {/* Informations sur l'apprenant */}
        <Card style={styles.infoCard}>
          <Card.Content>
            <Title>Informations de l'apprenant</Title>
            <Text style={styles.infoText}>
              <Text style={styles.label}>Nom : </Text>
              {learner.user.firstName} {learner.user.lastName}
            </Text>
            <Text style={styles.infoText}>
              <Text style={styles.label}>CIN : </Text>
              {learner.cin}
            </Text>
            <Text style={styles.infoText}>
              <Text style={styles.label}>Email : </Text>
              {learner.user.email}
            </Text>
            <Text style={styles.infoText}>
              <Text style={styles.label}>Statut : </Text>
              {learner.status}
            </Text>
          </Card.Content>
        </Card>

        {/* Composant de téléchargement de photo */}
        <PhotoUploader
          learnerId={id}
          currentPhoto={photoDocument}
          onPhotoUploaded={handlePhotoUploaded}
          editable={true}
          showStatus={true}
        />

        {/* Informations sur le processus de validation */}
        <Card style={styles.processCard}>
          <Card.Content>
            <Title>Processus de validation</Title>
            <View style={styles.processStep}>
              <Text style={styles.stepNumber}>1.</Text>
              <Text style={styles.stepText}>
                Téléchargez votre photo d'identité en respectant les instructions
              </Text>
            </View>
            <View style={styles.processStep}>
              <Text style={styles.stepNumber}>2.</Text>
              <Text style={styles.stepText}>
                Votre photo sera examinée par le secrétariat
              </Text>
            </View>
            <View style={styles.processStep}>
              <Text style={styles.stepNumber}>3.</Text>
              <Text style={styles.stepText}>
                Vous recevrez une notification du résultat de la validation
              </Text>
            </View>
            <View style={styles.processStep}>
              <Text style={styles.stepNumber}>4.</Text>
              <Text style={styles.stepText}>
                Si rejetée, vous pourrez télécharger une nouvelle photo
              </Text>
            </View>
          </Card.Content>
        </Card>

        {/* Conseils pour une bonne photo */}
        <Card style={styles.tipsCard}>
          <Card.Content>
            <Title>Conseils pour une photo réussie</Title>
            
            <Text style={styles.tipCategory}>✅ À faire :</Text>
            <Text style={styles.tipText}>• Utilisez un fond neutre (blanc ou bleu clair)</Text>
            <Text style={styles.tipText}>• Regardez directement l'objectif</Text>
            <Text style={styles.tipText}>• Gardez une expression neutre</Text>
            <Text style={styles.tipText}>• Assurez-vous que votre visage est bien éclairé</Text>
            <Text style={styles.tipText}>• Portez des vêtements de couleur unie</Text>
            
            <Text style={styles.tipCategory}>❌ À éviter :</Text>
            <Text style={styles.tipText}>• Lunettes de soleil ou reflets sur les lunettes</Text>
            <Text style={styles.tipText}>• Chapeaux ou couvre-chefs (sauf religieux)</Text>
            <Text style={styles.tipText}>• Photos floues ou mal cadrées</Text>
            <Text style={styles.tipText}>• Sourire excessif ou grimaces</Text>
            <Text style={styles.tipText}>• Arrière-plan encombré</Text>
          </Card.Content>
        </Card>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollContainer: {
    flex: 1,
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  errorText: {
    fontSize: 16,
    color: '#F44336',
    marginBottom: 16,
    textAlign: 'center',
  },
  infoCard: {
    marginBottom: 16,
  },
  infoText: {
    fontSize: 16,
    marginBottom: 8,
  },
  label: {
    fontWeight: 'bold',
    color: '#333',
  },
  processCard: {
    marginBottom: 16,
  },
  processStep: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  stepNumber: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#6200ee',
    marginRight: 8,
    minWidth: 20,
  },
  stepText: {
    fontSize: 14,
    flex: 1,
    lineHeight: 20,
  },
  tipsCard: {
    marginBottom: 16,
  },
  tipCategory: {
    fontSize: 16,
    fontWeight: 'bold',
    marginTop: 12,
    marginBottom: 8,
    color: '#333',
  },
  tipText: {
    fontSize: 14,
    marginBottom: 4,
    marginLeft: 8,
    color: '#666',
  },
});

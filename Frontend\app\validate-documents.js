import React, { useState } from 'react';
import { View, StyleSheet, ScrollView, Alert, Image, Modal } from 'react-native';
import {
  Text,
  Card,
  Button,
  Surface,
  ActivityIndicator,
  Chip,
  TextInput,
  IconButton
} from 'react-native-paper';
import { useRouter } from 'expo-router';
import { useMutation, useQuery } from '@apollo/client';
import { useAuth } from '../contexts/AuthContext';
import { GET_LEARNERS } from '../services/graphql/queries';
import { VALIDATE_DOCUMENT } from '../services/graphql/mutations';
import { MaterialIcons } from '@expo/vector-icons';

export default function ValidateDocumentsScreen() {
  const router = useRouter();
  const { user } = useAuth();
  const [selectedDocument, setSelectedDocument] = useState(null);
  const [modalVisible, setModalVisible] = useState(false);
  const [validationComment, setValidationComment] = useState('');
  const [validatingDocument, setValidatingDocument] = useState(null);

  // Get all learners with their documents
  const { loading, error, data, refetch } = useQuery(GET_LEARNERS);

  const [validateDocument] = useMutation(VALIDATE_DOCUMENT, {
    refetchQueries: [{ query: GET_LEARNERS }],
    onCompleted: () => {
      Alert.alert('Succès', 'Document validé avec succès');
      setValidationComment('');
      setValidatingDocument(null);
    },
    onError: (error) => {
      console.error('Error validating document:', error);
      Alert.alert('Erreur', 'Erreur lors de la validation du document');
    }
  });

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" />
        <Text style={styles.loadingText}>Chargement...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>Erreur de chargement</Text>
        <Text>{error.message}</Text>
      </View>
    );
  }

  // Vérifier si l'utilisateur est secrétaire ou administrateur
  if (user?.role !== 'SECRETAIRE' && user?.role !== 'ADMINISTRATEUR') {
    return (
      <View style={styles.container}>
        <Card style={styles.errorCard}>
          <Card.Content>
            <Text style={styles.errorTitle}>Accès non autorisé</Text>
            <Text style={styles.errorMessage}>
              Cette page est réservée aux secrétaires et administrateurs pour valider les documents.
            </Text>
            <Button
              mode="contained"
              onPress={() => router.back()}
              style={styles.backButton}
            >
              Retour
            </Button>
          </Card.Content>
        </Card>
      </View>
    );
  }

  const learners = data?.learners || [];

  // Configuration des types de documents
  const documentConfig = {
    cin: {
      title: 'CIN',
      icon: 'credit-card',
      color: '#2196F3'
    },
    medicalCertificate: {
      title: 'Certificat médical',
      icon: 'local-hospital',
      color: '#4CAF50'
    },
    photo: {
      title: 'Photo d\'identité',
      icon: 'photo-camera',
      color: '#FF9800'
    }
  };

  // Configuration des statuts
  const statusConfig = {
    EN_ATTENTE: {
      label: 'En attente',
      color: '#FF9800',
      backgroundColor: '#FFF3E0'
    },
    ACCEPTE: {
      label: 'Accepté',
      color: '#4CAF50',
      backgroundColor: '#E8F5E8'
    },
    REFUSE: {
      label: 'Refusé',
      color: '#F44336',
      backgroundColor: '#FFEBEE'
    }
  };

  const openDocument = (learner, docType, document) => {
    setSelectedDocument({ learner, docType, document });
    setModalVisible(true);
  };

  const closeModal = () => {
    setModalVisible(false);
    setSelectedDocument(null);
    setValidationComment('');
  };

  const handleValidateDocument = async (status) => {
    if (!selectedDocument) return;

    const { learner, docType } = selectedDocument;

    setValidatingDocument({ learnerId: learner.id, docType, status });

    try {
      await validateDocument({
        variables: {
          input: {
            learnerId: learner.id,
            documentType: docType,
            status: status,
            comment: validationComment || (status === 'ACCEPTE' ? 'Document validé' : 'Document rejeté')
          }
        }
      });
      closeModal();
    } catch (error) {
      console.error('Error validating document:', error);
    } finally {
      setValidatingDocument(null);
    }
  };

  const renderDocumentCard = (learner, docType, document) => {
    const config = documentConfig[docType];
    const status = statusConfig[document?.status || 'EN_ATTENTE'];

    if (!document || !document.file) {
      return null; // Ne pas afficher les documents non uploadés
    }

    return (
      <Card key={`${learner.id}-${docType}`} style={styles.documentCard}>
        <Card.Content>
          <View style={styles.documentHeader}>
            <View style={styles.learnerInfo}>
              <Text style={styles.learnerName}>
                {learner.user.firstName} {learner.user.lastName}
              </Text>
              <Text style={styles.learnerEmail}>{learner.user.email}</Text>
            </View>
            <Chip
              mode="flat"
              style={[styles.statusChip, { backgroundColor: status.backgroundColor }]}
              textStyle={[styles.chipText, { color: status.color }]}
            >
              {status.label}
            </Chip>
          </View>

          <View style={styles.documentInfo}>
            <MaterialIcons
              name={config.icon}
              size={20}
              color={config.color}
            />
            <Text style={[styles.documentTitle, { color: config.color }]}>
              {config.title}
            </Text>
          </View>

          <Text style={styles.uploadDate}>
            📅 Téléversé le : {new Date(document.uploadedAt).toLocaleDateString('fr-FR')}
          </Text>

          <View style={styles.documentActions}>
            <Button
              mode="outlined"
              onPress={() => openDocument(learner, docType, document)}
              icon="visibility"
              style={styles.actionButton}
            >
              Examiner
            </Button>

            {document.status === 'EN_ATTENTE' && (
              <>
                <Button
                  mode="contained"
                  onPress={() => {
                    setSelectedDocument({ learner, docType, document });
                    handleValidateDocument('ACCEPTE');
                  }}
                  icon="check"
                  style={[styles.actionButton, styles.acceptButton]}
                  loading={validatingDocument?.learnerId === learner.id &&
                           validatingDocument?.docType === docType &&
                           validatingDocument?.status === 'ACCEPTE'}
                >
                  Accepter
                </Button>
                <Button
                  mode="contained"
                  onPress={() => {
                    setSelectedDocument({ learner, docType, document });
                    setModalVisible(true);
                  }}
                  icon="close"
                  style={[styles.actionButton, styles.rejectButton]}
                >
                  Rejeter
                </Button>
              </>
            )}
          </View>
        </Card.Content>
      </Card>
    );
  };

  // Filtrer les apprenants qui ont des documents à valider
  const learnersWithDocuments = learners.filter(learner =>
    learner.documents &&
    Object.values(learner.documents).some(doc => doc && doc.file)
  );

  return (
    <View style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.contentContainer}
      >
        <Card style={styles.headerCard}>
          <Card.Content>
            <Text style={styles.title}>📋 Validation des documents</Text>
            <Text style={styles.description}>
              Examinez et validez les documents téléversés par les apprenants
            </Text>

            <View style={styles.statsContainer}>
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>
                  {learnersWithDocuments.length}
                </Text>
                <Text style={styles.statLabel}>Apprenants</Text>
              </View>
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>
                  {learnersWithDocuments.reduce((total, learner) =>
                    total + Object.values(learner.documents || {}).filter(doc =>
                      doc && doc.file && doc.status === 'EN_ATTENTE'
                    ).length, 0
                  )}
                </Text>
                <Text style={styles.statLabel}>En attente</Text>
              </View>
            </View>
          </Card.Content>
        </Card>

        {learnersWithDocuments.length === 0 ? (
          <Card style={styles.emptyCard}>
            <Card.Content>
              <Text style={styles.emptyTitle}>Aucun document à valider</Text>
              <Text style={styles.emptyMessage}>
                Aucun apprenant n'a téléversé de documents pour le moment.
              </Text>
            </Card.Content>
          </Card>
        ) : (
          learnersWithDocuments.map(learner =>
            Object.entries(learner.documents || {}).map(([docType, document]) =>
              renderDocumentCard(learner, docType, document)
            )
          ).flat()
        )}
      </ScrollView>

      {/* Modal pour examiner et valider les documents */}
      <Modal
        visible={modalVisible}
        transparent={true}
        animationType="slide"
        onRequestClose={closeModal}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>
                {selectedDocument && documentConfig[selectedDocument.docType]?.title}
              </Text>
              <IconButton
                icon="close"
                size={24}
                onPress={closeModal}
              />
            </View>

            {selectedDocument && (
              <>
                <Text style={styles.modalLearnerInfo}>
                  {selectedDocument.learner.user.firstName} {selectedDocument.learner.user.lastName}
                </Text>

                <Image
                  source={{ uri: selectedDocument.document.file }}
                  style={styles.fullImage}
                  resizeMode="contain"
                />

                <TextInput
                  label="Commentaire (optionnel)"
                  value={validationComment}
                  onChangeText={setValidationComment}
                  multiline
                  numberOfLines={3}
                  style={styles.commentInput}
                />

                <View style={styles.modalActions}>
                  <Button
                    mode="contained"
                    onPress={() => handleValidateDocument('ACCEPTE')}
                    icon="check"
                    style={styles.acceptButton}
                    loading={validatingDocument?.status === 'ACCEPTE'}
                  >
                    Accepter
                  </Button>
                  <Button
                    mode="contained"
                    onPress={() => handleValidateDocument('REFUSE')}
                    icon="close"
                    style={styles.rejectButton}
                    loading={validatingDocument?.status === 'REFUSE'}
                  >
                    Rejeter
                  </Button>
                </View>
              </>
            )}
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollView: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
    paddingBottom: 50,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 18,
    color: '#F44336',
    marginBottom: 10,
    textAlign: 'center',
  },
  errorCard: {
    margin: 16,
    elevation: 4,
  },
  errorTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#F44336',
    marginBottom: 12,
    textAlign: 'center',
  },
  errorMessage: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 20,
    lineHeight: 24,
  },
  backButton: {
    marginTop: 10,
  },
  headerCard: {
    elevation: 4,
    marginBottom: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
    textAlign: 'center',
    color: '#333',
  },
  description: {
    fontSize: 16,
    color: '#666',
    marginBottom: 20,
    lineHeight: 24,
    textAlign: 'center',
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    backgroundColor: '#f8f9fa',
    padding: 16,
    borderRadius: 8,
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2196F3',
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
    marginTop: 4,
  },
  documentCard: {
    marginBottom: 16,
    elevation: 2,
  },
  documentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  learnerInfo: {
    flex: 1,
  },
  learnerName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  learnerEmail: {
    fontSize: 12,
    color: '#666',
  },
  statusChip: {
    marginLeft: 8,
  },
  chipText: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  documentInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  documentTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  uploadDate: {
    fontSize: 12,
    color: '#666',
    marginBottom: 16,
  },
  documentActions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    flex: 1,
  },
  acceptButton: {
    backgroundColor: '#4CAF50',
  },
  rejectButton: {
    backgroundColor: '#F44336',
  },
  emptyCard: {
    elevation: 2,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#666',
    textAlign: 'center',
    marginBottom: 8,
  },
  emptyMessage: {
    fontSize: 14,
    color: '#999',
    textAlign: 'center',
    lineHeight: 20,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    width: '90%',
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  modalLearnerInfo: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 16,
  },
  fullImage: {
    width: '100%',
    height: 250,
    borderRadius: 8,
    marginBottom: 16,
  },
  commentInput: {
    marginBottom: 16,
  },
  modalActions: {
    flexDirection: 'row',
    gap: 12,
  },
});

export default ValidateDocumentsScreen;

import React, { useState } from 'react';
import { View, StyleSheet, Alert } from 'react-native';
import { 
  Text, 
  Card, 
  Title, 
  TextInput, 
  Button, 
  HelperText,
  Appbar
} from 'react-native-paper';
import { router } from 'expo-router';
import { useMutation } from '@apollo/client';
import { LOGIN_USER } from '../services/graphql/mutations';
import AsyncStorage from '@react-native-async-storage/async-storage';

export default function AdminLoginScreen() {
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  });
  const [errors, setErrors] = useState({});

  const [loginUser, { loading }] = useMutation(LOGIN_USER, {
    onCompleted: async (data) => {
      try {
        // Stocker le token
        await AsyncStorage.setItem('token', data.loginUser.token);
        await AsyncStorage.setItem('user', JSON.stringify(data.loginUser.user));
        
        // Vérifier le rôle
        const userRole = data.loginUser.user.role;
        if (userRole === 'ADMINISTRATEUR' || userRole === 'SECRETAIRE') {
          Alert.alert(
            'Connexion réussie',
            `Bienvenue ${data.loginUser.user.firstName} !`,
            [
              {
                text: 'OK',
                onPress: () => router.push('/learners')
              }
            ]
          );
        } else {
          Alert.alert(
            'Accès refusé',
            'Seuls les administrateurs et secrétaires peuvent accéder à cette section.',
            [
              {
                text: 'OK',
                onPress: () => router.push('/')
              }
            ]
          );
        }
      } catch (error) {
        console.error('Error storing token:', error);
      }
    },
    onError: (error) => {
      Alert.alert(
        'Erreur de connexion',
        error.message || 'Email ou mot de passe incorrect'
      );
    }
  });

  const validateForm = () => {
    const newErrors = {};

    if (!formData.email.trim()) {
      newErrors.email = 'L\'email est requis';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Format d\'email invalide';
    }

    if (!formData.password.trim()) {
      newErrors.password = 'Le mot de passe est requis';
    } else if (formData.password.length < 6) {
      newErrors.password = 'Le mot de passe doit contenir au moins 6 caractères';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    try {
      await loginUser({
        variables: {
          email: formData.email,
          password: formData.password
        }
      });
    } catch (error) {
      console.error('Login error:', error);
    }
  };

  const updateFormData = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: null }));
    }
  };

  return (
    <View style={styles.container}>
      <Appbar.Header style={styles.header}>
        <Appbar.BackAction onPress={() => router.back()} iconColor="#fff" />
        <Appbar.Content title="Connexion Administration" titleStyle={styles.headerTitle} />
      </Appbar.Header>

      <View style={styles.content}>
        <Card style={styles.card}>
          <Card.Content>
            <Title style={styles.title}>Connexion Administration</Title>
            <Text style={styles.subtitle}>
              Réservé aux administrateurs et secrétaires
            </Text>

            <TextInput
              label="Email *"
              value={formData.email}
              onChangeText={(value) => updateFormData('email', value)}
              keyboardType="email-address"
              autoCapitalize="none"
              error={!!errors.email}
              style={styles.input}
              left={<TextInput.Icon icon="email" />}
            />
            <HelperText type="error" visible={!!errors.email}>
              {errors.email}
            </HelperText>

            <TextInput
              label="Mot de passe *"
              value={formData.password}
              onChangeText={(value) => updateFormData('password', value)}
              secureTextEntry
              error={!!errors.password}
              style={styles.input}
              left={<TextInput.Icon icon="lock" />}
            />
            <HelperText type="error" visible={!!errors.password}>
              {errors.password}
            </HelperText>

            <Button
              mode="contained"
              onPress={handleSubmit}
              loading={loading}
              disabled={loading}
              style={styles.loginButton}
            >
              Se connecter
            </Button>

            <View style={styles.infoContainer}>
              <Text style={styles.infoTitle}>Comptes de test :</Text>
              <Text style={styles.infoText}>
                • Administrateur : <EMAIL> / password123
              </Text>
              <Text style={styles.infoText}>
                • Secrétaire : <EMAIL> / password123
              </Text>
            </View>
          </Card.Content>
        </Card>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#6200ee',
  },
  headerTitle: {
    color: '#fff',
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    padding: 16,
  },
  card: {
    elevation: 4,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
    textAlign: 'center',
    color: '#6200ee',
  },
  subtitle: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginBottom: 24,
  },
  input: {
    marginBottom: 8,
  },
  loginButton: {
    marginTop: 16,
    paddingVertical: 8,
  },
  infoContainer: {
    marginTop: 24,
    padding: 16,
    backgroundColor: '#e3f2fd',
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: '#2196F3',
  },
  infoTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#1976D2',
    marginBottom: 8,
  },
  infoText: {
    fontSize: 12,
    color: '#1565C0',
    marginBottom: 4,
  },
});

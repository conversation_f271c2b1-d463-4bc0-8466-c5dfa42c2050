import React, { useState } from 'react';
import { View, StyleSheet, Alert, Image } from 'react-native';
import {
  Text,
  Button,
  Card,
  Surface,
  ActivityIndicator,
  IconButton,
  Chip
} from 'react-native-paper';
import { MaterialIcons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';

const DocumentUpload = ({ 
  title, 
  description, 
  documentType, 
  onDocumentSelected, 
  selectedDocument,
  required = false,
  icon = "description"
}) => {
  const [uploading, setUploading] = useState(false);

  // Configuration des types de documents
  const documentConfig = {
    cin: {
      title: 'Carte d\'identité nationale',
      description: 'Photo recto-verso de votre CIN',
      icon: 'credit-card',
      color: '#2196F3'
    },
    medicalCertificate: {
      title: 'Certificat médical',
      description: 'Certificat médical d\'aptitude à la conduite',
      icon: 'local-hospital',
      color: '#4CAF50'
    },
    photo: {
      title: 'Photo d\'identité',
      description: 'Photo d\'identité récente (format passeport)',
      icon: 'photo-camera',
      color: '#FF9800'
    }
  };

  const config = documentConfig[documentType] || {
    title: title,
    description: description,
    icon: icon,
    color: '#6200ee'
  };

  // Demander les permissions
  const requestPermissions = async () => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert(
        'Permission requise',
        'Nous avons besoin de la permission d\'accéder à vos photos pour télécharger les documents.',
        [{ text: 'OK' }]
      );
      return false;
    }
    return true;
  };

  // Sélectionner depuis la galerie
  const pickFromGallery = async () => {
    const hasPermission = await requestPermissions();
    if (!hasPermission) return;

    setUploading(true);
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: documentType === 'photo' ? [1, 1] : [4, 3],
        quality: 0.8,
        base64: true,
      });

      if (!result.canceled && result.assets[0]) {
        const document = {
          uri: result.assets[0].uri,
          base64: `data:image/jpeg;base64,${result.assets[0].base64}`,
          type: documentType,
          name: `${documentType}_${Date.now()}.jpg`
        };
        onDocumentSelected(documentType, document);
      }
    } catch (error) {
      console.error('Error picking document:', error);
      Alert.alert('Erreur', 'Impossible de sélectionner le document');
    } finally {
      setUploading(false);
    }
  };

  // Prendre une photo
  const takePhoto = async () => {
    const { status } = await ImagePicker.requestCameraPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert(
        'Permission requise',
        'Nous avons besoin de la permission d\'accéder à votre caméra.',
        [{ text: 'OK' }]
      );
      return;
    }

    setUploading(true);
    try {
      const result = await ImagePicker.launchCameraAsync({
        allowsEditing: true,
        aspect: documentType === 'photo' ? [1, 1] : [4, 3],
        quality: 0.8,
        base64: true,
      });

      if (!result.canceled && result.assets[0]) {
        const document = {
          uri: result.assets[0].uri,
          base64: `data:image/jpeg;base64,${result.assets[0].base64}`,
          type: documentType,
          name: `${documentType}_${Date.now()}.jpg`
        };
        onDocumentSelected(documentType, document);
      }
    } catch (error) {
      console.error('Error taking photo:', error);
      Alert.alert('Erreur', 'Impossible de prendre la photo');
    } finally {
      setUploading(false);
    }
  };

  // Supprimer le document
  const removeDocument = () => {
    Alert.alert(
      'Supprimer le document',
      'Êtes-vous sûr de vouloir supprimer ce document ?',
      [
        { text: 'Annuler', style: 'cancel' },
        {
          text: 'Supprimer',
          style: 'destructive',
          onPress: () => onDocumentSelected(documentType, null)
        }
      ]
    );
  };

  return (
    <Card style={styles.container}>
      <Card.Content>
        <View style={styles.header}>
          <View style={styles.titleContainer}>
            <MaterialIcons 
              name={config.icon} 
              size={24} 
              color={config.color} 
            />
            <Text style={[styles.title, { color: config.color }]}>
              {config.title}
              {required && <Text style={styles.required}> *</Text>}
            </Text>
          </View>
          {selectedDocument && (
            <Chip 
              mode="flat" 
              style={[styles.statusChip, { backgroundColor: config.color }]}
              textStyle={styles.chipText}
              icon="check"
            >
              Ajouté
            </Chip>
          )}
        </View>

        <Text style={styles.description}>{config.description}</Text>

        {selectedDocument ? (
          <View style={styles.documentContainer}>
            <Surface style={styles.previewContainer}>
              <Image 
                source={{ uri: selectedDocument.uri }} 
                style={styles.previewImage}
                resizeMode="cover"
              />
              <View style={styles.previewOverlay}>
                <IconButton
                  icon="delete"
                  iconColor="white"
                  containerColor="rgba(244, 67, 54, 0.8)"
                  size={20}
                  onPress={removeDocument}
                />
              </View>
            </Surface>
            <Text style={styles.documentName}>{selectedDocument.name}</Text>
          </View>
        ) : (
          <View style={styles.uploadContainer}>
            {uploading ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="small" color={config.color} />
                <Text style={styles.loadingText}>Traitement...</Text>
              </View>
            ) : (
              <View style={styles.buttonContainer}>
                <Button
                  mode="outlined"
                  onPress={pickFromGallery}
                  icon="image"
                  style={[styles.uploadButton, { borderColor: config.color }]}
                  textColor={config.color}
                >
                  Galerie
                </Button>
                <Button
                  mode="outlined"
                  onPress={takePhoto}
                  icon="camera"
                  style={[styles.uploadButton, { borderColor: config.color }]}
                  textColor={config.color}
                >
                  Caméra
                </Button>
              </View>
            )}
          </View>
        )}

        {/* Conseils pour le document */}
        <View style={styles.tipsContainer}>
          <Text style={styles.tipsTitle}>💡 Conseils :</Text>
          {documentType === 'cin' && (
            <>
              <Text style={styles.tipText}>• Photographiez le recto ET le verso</Text>
              <Text style={styles.tipText}>• Assurez-vous que le texte est lisible</Text>
              <Text style={styles.tipText}>• Évitez les reflets et ombres</Text>
            </>
          )}
          {documentType === 'medicalCertificate' && (
            <>
              <Text style={styles.tipText}>• Le certificat doit dater de moins de 3 mois</Text>
              <Text style={styles.tipText}>• Doit mentionner l'aptitude à la conduite</Text>
              <Text style={styles.tipText}>• Signature du médecin obligatoire</Text>
            </>
          )}
          {documentType === 'photo' && (
            <>
              <Text style={styles.tipText}>• Photo récente (moins de 6 mois)</Text>
              <Text style={styles.tipText}>• Fond neutre et uniforme</Text>
              <Text style={styles.tipText}>• Visage bien visible et centré</Text>
            </>
          )}
        </View>
      </Card.Content>
    </Card>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
    elevation: 2,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  required: {
    color: '#F44336',
  },
  statusChip: {
    marginLeft: 8,
  },
  chipText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  description: {
    fontSize: 14,
    color: '#666',
    marginBottom: 16,
  },
  documentContainer: {
    alignItems: 'center',
    marginBottom: 16,
  },
  previewContainer: {
    position: 'relative',
    borderRadius: 8,
    overflow: 'hidden',
    elevation: 2,
  },
  previewImage: {
    width: 120,
    height: 90,
    borderRadius: 8,
  },
  previewOverlay: {
    position: 'absolute',
    top: 4,
    right: 4,
  },
  documentName: {
    fontSize: 12,
    color: '#666',
    marginTop: 8,
    textAlign: 'center',
  },
  uploadContainer: {
    marginBottom: 16,
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
  },
  loadingText: {
    marginLeft: 8,
    fontSize: 14,
    color: '#666',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    gap: 12,
  },
  uploadButton: {
    flex: 1,
  },
  tipsContainer: {
    backgroundColor: '#f8f9fa',
    padding: 12,
    borderRadius: 8,
    borderLeftWidth: 3,
    borderLeftColor: '#2196F3',
  },
  tipsTitle: {
    fontSize: 13,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 6,
  },
  tipText: {
    fontSize: 12,
    color: '#666',
    marginBottom: 2,
  },
});

export default DocumentUpload;
